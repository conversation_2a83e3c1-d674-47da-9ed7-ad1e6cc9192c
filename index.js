const express = require("express");
const http = require("http");
const cors = require("cors");

const { connectDB } = require("./config/db");
const authRouter = require("./routes/authRoutes");
const cookieParser = require("cookie-parser");
const userRouter = require("./routes/userRoutes");
const errorHandler = require("./middlewares/errorHandler");
const hostRouter = require("./routes/hostRoutes");
const app = express();
const passport = require("passport");
const cuisineRouter = require("./routes/cuisineRoutes");
const diningRouter = require("./routes/diningRoutes");
const dishRouter = require("./routes/dishRoutes");
const addressRouter = require("./routes/addressRoutes");
const cartRouter = require("./routes/cartRoutes");
const orderRouter = require("./routes/orderRoutes");
const { createRouteHandler } = require("uploadthing/express");
const {
  uploadRouter,
  uploadRouterSingleImage,
} = require("./config/uploadthing");
const userProfileRoutes = require("./routes/userProfileRoutes");
const ingredientRouter = require("./routes/ingredientRoutes");
const exchangeRaterouter = require("./routes/exchangeRateRoutes");
const requestsRouter = require("./routes/requestRoutes");
const notificationRouter = require("./routes/notificationRoutes");
const chatRouter = require("./routes/chatRoutes");
const socketManager = require("./utils/websocket");
const stripeRouter = require("./routes/stripeRoutes");
const testRoutes = require("./test/ws");
const session = require("express-session");
const stripe = require("./config/stripe");
const { Order } = require("./mongodb/model");
const orderController = require("./controllers/orderController");

require("dotenv").config();
require("./config/OAuthStartergy"); // Load the Facebook strategy

const port = process.env.PORT || 5000;
const allowedOrigins = JSON.parse(process.env.CORS_ORIGIN);

app.use(
  cors({
    // Extract CORS_ORIGIN from the environment (array)
    origin: allowedOrigins,
    credentials: true,
  })
);

// @route    POST api/stripe/webhook
// @desc     Handle Stripe webhooks
// @access   Public
app.post(
  "/webhook",
  express.raw({ type: "application/json" }),
  async (req, res) => {
    const signature = req.headers["stripe-signature"];

    let event;

    try {
      event = stripe.webhooks.constructEvent(
        req.body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET ||
          "whsec_f0374ca628468a0da519db3d9c5f7a9404743fba36e7d14f28c5699e9ddf6e7f"
      );
    } catch (err) {
      console.error(`Webhook signature verification failed: ${err.message}`);
      return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    // Handle the event
    console.log(
      `🎯 DEBUG: Stripe webhook received - Event type: ${event.type}`
    );
    switch (event.type) {
      case "payment_intent.succeeded":
        const paymentIntent = event.data.object;
        console.log(
          `💳 DEBUG: payment_intent.succeeded webhook for order ${paymentIntent.metadata.orderId}`
        );
        // Update order status
        if (paymentIntent.metadata.orderId) {
          await orderController.handlePaymentSuccess(
            paymentIntent.metadata.orderId,
            "card"
          );
        }
        break;

      case "checkout.session.completed":
        const session = event.data.object;
        console.log(
          `🛒 DEBUG: checkout.session.completed webhook for order ${session.metadata.orderId}`
        );
        if (session.metadata.orderId) {
          await orderController.handlePaymentSuccess(
            session.metadata.orderId,
            "stripe_checkout",
            session.receipt_url
          );
        }
        break;

      case "payment_intent.payment_failed":
        const failedPayment = event.data.object;
        console.log("Payment failed =========> ", failedPayment);
        if (failedPayment.metadata.orderId) {
          // Update order payment status to failed
          await Order.findByIdAndUpdate(failedPayment.metadata.orderId, {
            payment: "failed",
          });
        }
        break;

      default:
        console.log(`Unhandled event type ${event.type}`);
    }

    // Return a 200 response to acknowledge receipt of the event
    res.json({ received: true });
  }
);

app.use(express.json());
app.use(cookieParser());

const server = http.createServer(app);
socketManager.initialize(server);

app.use(
  session({
    secret: "keyboard cat",
    resave: false,
    saveUninitialized: true,

    cookie: {
      maxAge: 24 * 60 * 60 * 1000, // Persistent cookie
      secure: process.env.NODE_ENV === "production" ? true : false, // Use HTTPS
      httpOnly: process.env.NODE_ENV === "production" ? true : false, // Prevent client-side JS from reading the cookie
      sameSite: process.env.NODE_ENV === "production" ? "none" : "lax", // Enforce CORS
    },
  })
);
app.enable("trust proxy");
app.use(passport.initialize());

// routes
app.use("/api/auth", authRouter);
app.use("/api/user", userRouter);
app.use("/api/host", hostRouter);
app.use("/api/cuisine", cuisineRouter);
app.use("/api/dining", diningRouter);
app.use("/api/dish", dishRouter);
app.use("/api/address", addressRouter);
app.use("/api/userprofiles", userProfileRoutes);
app.use("/api/cart", cartRouter);
app.use("/api/order", orderRouter);
app.use("/api/ingredient", ingredientRouter);
app.use("/api/exchangeRate", exchangeRaterouter);
app.use("/api/requests", requestsRouter);
app.use("/api/notifications", notificationRouter);
app.use("/api/chat", chatRouter);
app.use("/api/stripe", stripeRouter);
console.log("process.env.UPLOADTHING_TOKEN", process.env.UPLOADTHING_TOKEN);
app.use(
  "/api/upload",
  createRouteHandler({
    router: uploadRouter,
    config: {
      token:
        "************************************************************************************************************************************************************************",
      logLevel: "Debug",
      logFormat: "pretty",
      callbackUrl: "http://localhost:5000/api/uploadthing",
    },
  })
);
app.use(
  "/api/upload/single",
  createRouteHandler({
    router: uploadRouterSingleImage,
  })
);

// Add a dedicated callback endpoint
app.post("/api/uploadthing", (req, res) => {
  console.log("UploadThing callback received:", req.body);
  res.status(200).json({ success: true });
});

// Mount test routes
app.use("/api/test", testRoutes);

// handling errors
app.use(errorHandler);

//Connect to the database before listening
connectDB().then(() => {
  server.listen(port, () => {
    console.log("listening for requests");
  });
});
