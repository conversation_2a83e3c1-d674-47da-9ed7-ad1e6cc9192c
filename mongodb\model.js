//this file contains all the models
const mongoose = require("mongoose");
const userSchema = require("./schema/User");
const FederatedCredentialSchema = require("./schema/FederatedCredential");
const addressSchema = require("./schema/Address");
const cuisineSchema = require("./schema/Cuisine");
const diningLocationSchema = require("./schema/DiningLocation");
const hostSchema = require("./schema/Host");
const dishSchema = require("./schema/Dish");
const rating = require("./schema/Rating");
const userprofileSchema = require("./schema/UserProfile");
const { CartSchema, CartItemSchema } = require("./schema/Cart");
const OrderSchema = require("./schema/Order");
const ingredientSchema = require("./schema/Ingredient");
const exchangeRateSchema = require("./schema/ExchangeRate");
const requestSchema = require("./schema/Request");
const notificationSchema = require("./schema/Notification");
const conversationSchema = require("./schema/Conversation");
const messageSchema = require("./schema/Message");
const responseSchema = require("./schema/Response");

const userModel = mongoose.model("users", userSchema);
const FederatedCredentialModel = mongoose.model(
  "federatedcredentials",
  FederatedCredentialSchema
);
const Address = mongoose.model("Address", addressSchema);
const Cuisine = mongoose.model("Cuisine", cuisineSchema);
const DiningLocation = mongoose.model("DiningLocation", diningLocationSchema);
const Host = mongoose.model("Host", hostSchema);
const Dish = mongoose.model("Dish", dishSchema);
const Rating = mongoose.model("Rating", rating);
const UserProfile = mongoose.model("UserProfile", userprofileSchema);
const Cart = mongoose.model("Cart", CartSchema);
const CartItem = mongoose.model("CartItem", CartItemSchema);
const Order = mongoose.model("Order", OrderSchema);
const Ingredient = mongoose.model("Ingredient", ingredientSchema);
const ExchangeRate = mongoose.model("ExchangeRate", exchangeRateSchema);
const Request = mongoose.model("Request", requestSchema);
const Notification = mongoose.model("Notification", notificationSchema);
const Conversation = mongoose.model("Conversation", conversationSchema);
const Message = mongoose.model("Message", messageSchema);
const Response = mongoose.model("Response", responseSchema);

module.exports = {
  userModel,
  FederatedCredentialModel,
  Address,
  Cuisine,
  DiningLocation,
  Host,
  Dish,
  Rating,
  UserProfile,
  Cart,
  CartItem,
  Order,
  Ingredient,
  ExchangeRate,
  Request,
  Notification,
  Conversation,
  Message,
  Response,
};
