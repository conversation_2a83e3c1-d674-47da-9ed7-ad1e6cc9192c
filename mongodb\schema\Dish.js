const mongoose = require("mongoose");

// Ensure virtuals are included in JSON and object outputs
mongoose.Schema.set("toJSON", { virtuals: true });
const dishSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    price: {
      type: Number,
      required: true,
    },
    discount: {
      type: Number,
    },
    description: {
      type: String,
      required: true,
    },
    diningLocation: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "DiningLocation",
    },
    offering: {
      type: [String],
      enum: ["Dine In", "Take Away", "Delivery"],
    },
    ratings: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Rating",
      },
    ],
    averageRating: {
      type: Number,
      default: 0,
    },
    photos: {
      type: [String],
      validate: [arrayMinSize, "A dish must have at least one photo"],
      required: true,
    },
    ingredients: {
      type: [String],
      validate: [arrayMinSize, "A dish must have at least one ingredient"],
      required: true,
    },
    cuisine: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Cuisine",
      required: true,
    },
    subcuisine: {
      type: String,
    },
    published: {
      type: Boolean,
      default: false,
    },
    minServings: {
      type: Number,
      required: true,
    },
    maxServings: {
      type: Number,
      required: true,
    },
    premade: {
      type: Boolean,
      default: false,
    },
    request: {
      type: Boolean,
      default: false,
    },
    currency: {
      type: String,
      default: "USD",
    },
    isResponseSpecific: {
      type: Boolean,
      default: false,
    },
    originalRequestId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Request",
      default: null, // Or remove default if it should always be set for response-specific dishes
    },

    availability: [
      {
        date: {
          type: Date,
          required: true,
        },
        startTime: {
          type: String,
          required: true,
          validate: {
            validator: (v) => /^([01]\d|2[0-3]):([0-5]\d)$/.test(v),
            message: (props) =>
              `${props.value} is not a valid time format. Use HH:MM.`,
          },
        },
        endTime: {
          type: String,
          required: true,
          validate: {
            validator: (v) => /^([01]\d|2[0-3]):([0-5]\d)$/.test(v),
            message: (props) =>
              `${props.value} is not a valid time format. Use HH:MM.`,
          },
        },
        startTimeGMT: {
          type: Date,
          default: null,
        },
        endTimeGMT: {
          type: Date,
          default: null,
        },
      },
    ],
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// Validator for minimum array length
function arrayMinSize(val) {
  return val.length >= 1;
}

// Removed automatic population pre-hook to allow selective filtering
// Population will be handled explicitly in service layer

// Validator to ensure endTime is after startTime
dishSchema.path("availability").validate((value) => {
  if (value && value.length) {
    return value.every((slot) => {
      const start = new Date(`1970-01-01T${slot.startTime}:00`);
      const end = new Date(`1970-01-01T${slot.endTime}:00`);
      return end > start;
    });
  }
  return true;
}, "End time must be after start time");

// Virtual for dynamic dineInAvailable field
dishSchema.virtual("dineInAvailable").get(function () {
  if (
    !this.offering ||
    !this.offering.includes("Dine In") ||
    !Array.isArray(this.availability)
  ) {
    return false;
  }
  const now = new Date();
  return this.availability.some((slot) => {
    let start, end;
    if (slot.startTimeGMT && slot.endTimeGMT) {
      start = new Date(slot.startTimeGMT);
      end = new Date(slot.endTimeGMT);
    } else if (slot.date && slot.startTime && slot.endTime) {
      const dateStr = new Date(slot.date).toISOString().split("T")[0];
      start = new Date(`${dateStr}T${slot.startTime}:00.000Z`);
      dishSchema.set("toJSON", { virtuals: true });
      dishSchema.set("toObject", { virtuals: true });
      end = new Date(`${dateStr}T${slot.endTime}:00.000Z`);
    }
    return start && end && now >= start && now <= end;
  });
});

module.exports = dishSchema;
