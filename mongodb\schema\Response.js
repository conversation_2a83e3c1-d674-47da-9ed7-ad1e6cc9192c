const mongoose = require("mongoose");

const ResponseSchema = new mongoose.Schema(
  {
    request: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Request", // Link to the parent Request
      required: true,
      index: true,
    },
    host: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "users", // Link to the responding host
      required: true,
      index: true,
    },
    dish: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Dish", // Link to the unique Dish document created for this response
      required: true,
      index: true,
    },
    message: {
      type: String,
    },
    status: {
      type: String,
      enum: [
        "pending_user_action",
        "accepted_by_user",
        "rejected_by_user",
        "withdrawn_by_host",
      ],
      default: "pending_user_action",
    },
  },
  { timestamps: true }
);

// Optional: Compound index if frequently querying by request and status, or host and status
// ResponseSchema.index({ request: 1, status: 1 });
// ResponseSchema.index({ host: 1, status: 1 });

module.exports = ResponseSchema;
