// routes/api/stripe.js
const express = require("express");
const stripeRouter = express.Router();
const stripe = require("../config/stripe");
const { verifyToken } = require("../middlewares/authMiddleware");
const { Order } = require("../mongodb/model");
const orderController = require("../controllers/orderController");

// @route    POST api/stripe/create-payment-intent
// @desc     Create a payment intent for an order with multiple hosts
// @access   Private
stripeRouter.post(
  "/create-payment-intent",
  verifyToken,
  orderController.createPaymentIntent
);

// @route    POST api/stripe/create-checkout-session
// @desc     Create a checkout session for an order with multiple hosts
// @access   Private
stripeRouter.post(
  "/create-checkout-session",
  verifyToken,
  orderController.createCheckoutSession
);

// @route    GET api/stripe/session/:sessionId
// @desc     Get session details
// @access   Private
stripeRouter.get("/session/:sessionId", verifyToken, async (req, res) => {
  try {
    const { sessionId } = req.params;

    const session = await stripe.checkout.sessions.retrieve(sessionId);

    // Get order by session ID
    const order = await Order.findOne({ stripeSessionId: sessionId });

    if (!order) {
      return res.status(404).json({ msg: "Order not found" });
    }

    if (order.userId.toString() !== req.user.id) {
      return res.status(401).json({ msg: "User not authorized" });
    }

    // If payment was successful
    if (session.payment_status === "paid") {
      console.log(
        `🌐 DEBUG: Session retrieval endpoint calling handlePaymentSuccess for order ${order.id}`
      );
      // Handle successful payment
      await orderController.handlePaymentSuccess(
        order.id,
        "stripe_checkout",
        session.receipt_url
      );
    }

    res.json({ session, order });
  } catch (err) {
    console.error("Error retrieving session:", err);
    res.status(500).send("Server error");
  }
});

// @route    POST api/stripe/session/:sessionId/cancel
// @desc     Cancel a session
// @access   Private
stripeRouter.post(
  "/session/:sessionId/cancel",
  verifyToken,
  async (req, res) => {
    try {
      const { sessionId } = req.params;

      const session = await stripe.checkout.sessions.retrieve(sessionId);

      // Get order by session ID
      const order = await Order.findOne({ stripeSessionId: sessionId });

      if (!order) {
        return res.status(404).json({ msg: "Order not found" });
      }

      if (order.userId.toString() !== req.user.id) {
        return res.status(401).json({ msg: "User not authorized" });
      }

      if (session.payment_status === "paid") {
        return res.status(400).json({ msg: "Session already paid" });
      }

      // Cancel session
      if (session.payment_status === "open") {
        await stripe.checkout.sessions.expire(sessionId);
      }

      // Update order status

      order.hostGroups.forEach((group) => {
        group.status = "cancelled";
      });
      order.payment = "failed";
      await order.save();

      res.json({ msg: "Session cancelled", session, order });
    } catch (err) {
      console.error("Error cancelling session:", err);
      res.status(500).send("Server error");
    }
  }
);

module.exports = stripeRouter;
