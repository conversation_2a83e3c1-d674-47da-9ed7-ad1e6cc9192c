// hooks/orderHooks.js
const chatService = require("../services/chatService");

/**
 * Hooks for order-related events
 */
class OrderHooks {
  /**
   * Create a chat conversation when an order is created
   * @param {Object} order - The created order
   * @param {Object} hostId - The host ID
   * @param {Object} hostUser - The host user
   * @param {Object} hostGroup - The host group
   */
  async onOrderCreated(order, hostId, hostGroup, hostUser) {
    try {
      if (!order || !order._id) {
        console.error("Invalid order object provided to onOrderCreated hook");
        return;
      }

      // Extract user and host information from the order
      const userId = order.userId;

      // Get the host ID from the first host group
      // Assuming the first host group is the one we want to create a conversation with
      if (!order.hostGroups || order.hostGroups.length === 0) {
        console.error("Order has no host groups, cannot create conversation");
        return;
      }

      if (!userId || !hostId) {
        console.error(
          "Order is missing user or host ID, cannot create conversation"
        );
        return;
      }

      // Create metadata with order information
      const metadata = {
        orderNumber: order.orderNumber,
        orderDate: order.createdAt,
        orderTotal: hostGroup.subtotal,
      };

      // Create a conversation for this order
      const conversation = await chatService.createOrderConversation(
        order._id,
        userId,
        hostId,
        hostUser,
        metadata
      );

      console.log(
        `Created conversation ${conversation._id} for order ${order._id} between user ${userId} and host ${hostId}`
      );

      return conversation;
    } catch (error) {
      console.error("Error in onOrderCreated hook:", error);
    }
  }

  /**
   * Archive a chat conversation when an order is completed or cancelled
   * @param {Object} order - The updated order
   * @param {Object} options - Additional options
   */
  async onOrderStatusChanged(order, options = {}) {
    try {
      if (!order || !order._id) {
        console.error(
          "Invalid order object provided to onOrderStatusChanged hook"
        );
        return;
      }

      // Check if all host groups have a completed or cancelled status
      const allCompleted = order.hostGroups.every(
        (group) => group.status === "completed" || group.status === "cancelled"
      );

      // If all host groups are completed or cancelled, archive the conversation
      if (allCompleted) {
        const conversation = await chatService.archiveConversation(order._id);
        console.log(`Archived conversation for order ${order._id}`);
        return conversation;
      }
    } catch (error) {
      console.error("Error in onOrderStatusChanged hook:", error);
    }
  }
}

module.exports = new OrderHooks();
