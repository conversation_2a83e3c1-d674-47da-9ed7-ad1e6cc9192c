const { UN_AUTHENTICATED, UN_AUTHORIZED } = require("../constants/errorCodes");
const { extractUser } = require("../utils");
const AppError = require("../utils/error");
const jwt = require("jsonwebtoken");

/**
 ** Verify User Token
 */
const verifyToken = (req, res, next) => {
  const authHeader = req.headers["authorization"];
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return next(
      new AppError(
        {
          message: "No authorization token found",
        },
        UN_AUTHORIZED
      )
    );
  }

  const token = authHeader.split(" ")[1];

  try {
    const decodedUser = extractUser(token, process.env.JWT_SECRET_ACCESS_TOKEN);
    req.user = decodedUser;
    return next();
  } catch (err) {
    next(new AppError({ message: "Invalid Token" }, UN_AUTHENTICATED));
  }
};

/**
 * Get User Token
 */

const userToken = (req, res, next) => {
  const authHeader = req.headers["authorization"];
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return next();
  }

  const token = authHeader.split(" ")[1];

  try {
    const decodedUser = extractUser(token, process.env.JWT_SECRET_ACCESS_TOKEN);
    req.user = decodedUser;
    return next();
  } catch (err) {
    return next();
  }
};

// For testing only - creates a test token without verification
const createTestToken = (req, res) => {
  const { id, type } = req.body;

  if (!id || !type) {
    return res
      .status(400)
      .json({ error: "Missing required fields (id, type)" });
  }

  const token = jwt.sign({ id, type }, process.env.JWT_SECRET_ACCESS_TOKEN, {
    expiresIn: "1h",
  });

  res.json({ token });
};

module.exports = {
  verifyToken,
  userToken,
};
