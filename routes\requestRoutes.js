// src/routes/requestRoutes.js
const express = require("express");
const router = express.Router();
const requestController = require("../controllers/requestController");
const { verifyToken } = require("../middlewares/authMiddleware");

// Request routes
router.post("/", verifyToken, requestController.createRequest);

router.get("/feed", verifyToken, requestController.getRequestFeed);

router.get("/user", verifyToken, requestController.getUserRequests);

router.get("/host-responses", verifyToken, requestController.getHostResponses);

router.get("/:requestId", verifyToken, requestController.getRequestbyId);
// Response routes
router.get(
  // New route to get all responses for a request
  "/:requestId/responses",
  verifyToken,
  requestController.getResponsesForRequest
);

router.post(
  "/:requestId/responses",
  verifyToken,
  requestController.respondToRequest
);

router.post(
  "/:requestId/responses/:responseId/accept",
  verifyToken,
  requestController.acceptResponse
);

module.exports = router;
