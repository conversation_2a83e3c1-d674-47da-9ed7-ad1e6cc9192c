# Frontend Integration with Request System: Documentation Plan

## I. Introduction

A. Purpose of the document.
B. Overview of the 'request system' functionality (user requests a dish, hosts respond, user accepts a response).
C. General architectural approach (REST API for actions, WebSockets for real-time updates).

## II. Authentication and Authorization

A. **API Authentication:** 1. Method: JWT Bearer Token. 2. Header: `Authorization: Bearer <YOUR_JWT_TOKEN>`. 3. Obtaining the token (briefly mention login/auth endpoints, or refer to separate auth documentation if it exists). 4. Token expiration and refresh (if applicable, based on `middlewares/authMiddleware.js` which shows a 1hr expiry for test tokens).
B. **WebSocket Authentication:** 1. Method: JWT Token passed during connection. 2. Sources:
_ `Authorization` header (e.g., `socket.handshake.headers.authorization`).
_ `socket.handshake.auth.token`. \* `socket.handshake.query.token`. 3. Error handling for failed authentication (e.g., `AUTH_TOKEN_REQUIRED`, `AUTH_TOKEN_EXPIRED`, `AUTH_TOKEN_INVALID`).
C. User Roles (if applicable, e.g., 'user' vs 'host' and how it might affect API access or WebSocket behavior, based on `socket.user.type`).

## III. API Endpoints (Request System)

_For each endpoint, detail: HTTP Method, Full URL Path (e.g., `/api/requests`), Required Headers (e.g., `Authorization`, `Content-Type`), Request Body Schema (JSON, with field descriptions, types, and required status based on Mongoose schemas), Response Body Schema (JSON, with field descriptions and types), and Status Codes (Success and Error)._

A. **Create a New Dish Request** 1. Endpoint: `POST /requests` 2. Controller Logic: `requestController.createRequest` 3. Request Body Schema: Based on `dishRequestDetails` and `location` from `requestController.js`, referencing `DishRequestSchema` and `Address` schema. 4. Response Body Schema: The created Request object. 5. Status Codes: `201 Created`, `400 Bad Request`, `401 Unauthorized`, `500 Internal Server Error`.
B. **Get Request Feed (for Hosts)** 1. Endpoint: `GET /requests/feed` 2. Controller Logic: `requestController.getRequestFeed` 3. Query Parameters: `location` (string), `page` (number, default 1), `limit` (number, default 20). 4. Response Body Schema: Paginated list of active Request objects. 5. Status Codes: `200 OK`, `401 Unauthorized`, `500 Internal Server Error`.
C. **Get User's Own Requests** 1. Endpoint: `GET /requests/user` 2. Controller Logic: `requestController.getUserRequests` 3. Query Parameters: `status` (string, e.g., "pending_responses"), `page`, `limit`. 4. Response Body Schema: Paginated list of Request objects belonging to the authenticated user. 5. Status Codes: `200 OK`, `401 Unauthorized`, `500 Internal Server Error`.
D. **Get Specific Request by ID** 1. Endpoint: `GET /requests/:requestId` 2. Controller Logic: `requestController.getRequestbyId` 3. Response Body Schema: Single Request object. 4. Status Codes: `200 OK`, `401 Unauthorized`, `404 Not Found`, `500 Internal Server Error`.
E. **Get Host's Responses to Various Requests** 1. Endpoint: `GET /requests/host-responses` 2. Controller Logic: `requestController.getHostResponses` 3. Query Parameters: `status` (string), `page`, `limit`. 4. Response Body Schema: Paginated list of Response objects made by the authenticated host. 5. Status Codes: `200 OK`, `401 Unauthorized`, `500 Internal Server Error`.
F. **Get All Responses for a Specific Request** 1. Endpoint: `GET /requests/:requestId/responses` 2. Controller Logic: `requestController.getResponsesForRequest` 3. Query Parameters: `page`, `limit`. 4. Response Body Schema: Paginated list of Response objects for the given `requestId`. 5. Status Codes: `200 OK`, `401 Unauthorized`, `404 Not Found`, `500 Internal Server Error`.
G. **Respond to a Request (by Host)** 1. Endpoint: `POST /requests/:requestId/responses` 2. Controller Logic: `requestController.respondToRequest` 3. Request Body Schema: `message` (string) and `dishData` (based on `Dish` schema, noting `isResponseSpecific: true` and `originalRequestId`). 4. Response Body Schema: The created Response object. 5. Status Codes: `201 Created`, `400 Bad Request`, `401 Unauthorized`, `404 Not Found`, `500 Internal Server Error`.
H. **Accept a Response (by User who created the Request)** 1. Endpoint: `POST /requests/:requestId/responses/:responseId/accept` 2. Controller Logic: `requestController.acceptResponse` 3. Request Body Schema: (Likely empty). 4. Response Body Schema: The updated Request object. 5. Status Codes: `200 OK`, `400 Bad Request`, `401 Unauthorized`, `403 Forbidden`, `404 Not Found`, `500 Internal Server Error`.

## IV. WebSocket Communication (Request System)

A. **Connection:** 1. URI: `your_base_server_url/socket.io`. 2. Authentication: As detailed in section II.B. 3. Key Server-Sent Connection Events:
_ `connection:success`: Payload `{ message, userId, userType, socketId }`.
_ `error`: Payload `{ code, message, details }`.
B. **Client-Sent Events (for Hosts):** 1. **`host:register`**
_ Purpose: For a host client to subscribe to new request feeds based on location.
_ Payload: `{ location: string }`.
* Server Response/Confirmation: `host:registered` event with payload `{ success: true, location }`.
C. **Server-Sent Events (Real-time Updates for Request System):**
*For each event, detail: Event Name, Payload Schema (JSON, with field descriptions and types), and When/Why it's emitted._ 1. **`feed:newRequest`** (Emitted to hosts)
_ Payload: `{ request: NewRequestObject, location: string (city) }`.
_ Trigger: After a new request is successfully created via `POST /requests`. 2. **`request:newResponse`** (Emitted to the user who created the request)
_ Payload: `{ requestId: string, response: NewResponseObject }`.
_ Trigger: After a host successfully responds to a request via `POST /requests/:requestId/responses`. 3. **`response:accepted`** (Emitted to the host whose response was accepted)
_ Payload: `{ requestId: string, responseId: string, requestTitle: string }`.
_ Trigger: After a user successfully accepts a response via `POST /requests/:requestId/responses/:responseId/accept`. 4. **`request:updated`** (Emitted to the user who created the request, after they accept a response)
_ Payload: `{ request: UpdatedRequestObject }`.
_ Trigger: After a user successfully accepts a response, providing the updated request state. 5. **`feed:requestInactive`** (Emitted to relevant hosts)
_ Payload: `{ requestId: string }`.
_ Trigger: After a user successfully accepts a response via the `POST /requests/:requestId/responses/:responseId/accept` API endpoint. Emitted from `requestController.acceptResponse`.
_ Audience: Broadcast to hosts who might have originally seen this request in their feed (e.g., by location).

## V. Code Snippets (JavaScript Examples)

A. **API Calls:** 1. Example: Creating a new request using `fetch` or `axios`. 2. Example: Responding to a request. 3. Example: Accepting a response.
B. **WebSocket Interactions:** 1. Example: Connecting to the WebSocket server with authentication. 2. Example: Host client sending `host:register`. 3. Example: Client listening for `feed:newRequest`. 4. Example: Client listening for `request:newResponse`. 5. Example: Client listening for `response:accepted`.

## VI. Workflow Diagrams (Mermaid)

A. **User Request Creation and Host Notification Flow:**
```mermaid
sequenceDiagram
participant FE_User as Frontend (User)
participant API as Backend API
participant WS_Server as WebSocket Server
participant FE_Host as Frontend (Host)

        FE_User->>+API: POST /requests (Create Request)
        API-->>-FE_User: 201 Created (Request Object)
        API->>WS_Server: New Request Data (for broadcast)
        WS_Server-->>FE_Host: feed:newRequest (payload)
    ```

B. **Host Response and User Notification Flow:**
```mermaid
sequenceDiagram
participant FE_Host as Frontend (Host)
participant API as Backend API
participant WS_Server as WebSocket Server
participant FE_User as Frontend (User)

        FE_Host->>+API: POST /requests/{reqId}/responses (Submit Response)
        API-->>-FE_Host: 201 Created (Response Object)
        API->>WS_Server: New Response Data (for user)
        WS_Server-->>FE_User: request:newResponse (payload)
    ```

C. **User Accepts Response and Host Notification Flow:**
```mermaid
sequenceDiagram
participant FE_User as Frontend (User)
participant API as Backend API
participant WS_Server as WebSocket Server
participant FE_Chosen_Host as Frontend (Chosen Host)
participant FE_Other_Hosts as Frontend (Other Relevant Hosts)

        FE_User->>+API: POST /requests/{reqId}/responses/{respId}/accept (Accept Response)
        API-->>-FE_User: 200 OK (Updated Request Object)
        API->>WS_Server: Accepted Response Data (for chosen host)
        WS_Server-->>FE_Chosen_Host: response:accepted (payload)
        API->>WS_Server: Updated Request Data (for user self-update)
        WS_Server-->>FE_User: request:updated (payload)
        API->>WS_Server: Request Inactive Data (for other relevant hosts, using request's location)
        WS_Server-->>FE_Other_Hosts: feed:requestInactive (payload)
    ```

## VII. Appendix (Optional)

A. Full Mongoose Schemas (Request, Response, Dish, Address) for reference.
B. Common Error Codes and Meanings.

---

**Points to Note/Refine during actual documentation writing:**

- Ensure all schema definitions (request/response bodies for API, payloads for WebSockets) are precise, including data types, required fields, and enum values.
- The JWT token expiration and refresh strategy for API calls should be clearly stated if it's more complex than a simple expiration.
