# Dish Request System Refactor Plan

## 1. Introduction and Goal

This document outlines the plan to refactor the dish request system. The primary goal is to enhance scalability, data integrity, and clarity of the system by addressing limitations in the current data model and interaction flow.

## 2. Core Problem Addressed

The current system embeds `HostResponseSchema` within each `Request` document (as seen in `mongodb/schema/Request.js`). This architecture leads to:

- **Scalability Issues:** Potential for `Request` documents to exceed MongoDB's document size limits if a request receives many responses.
- **Inefficient Querying:** Difficulty and performance degradation when querying for host-specific responses or managing individual response states.
- **Complexity:** Increased complexity in updating and managing individual responses embedded within a larger document.

## 3. Overarching Solution

The cornerstone of the proposed refactor is to **decouple host responses into their own top-level MongoDB collection**. This change, combined with the clarified workflow where **a new, unique `Dish` document is created by the host for each specific response**, forms the basis of the new architecture.

## 4. Detailed Proposed Changes

### I. Data Model Restructuring

#### A. `Requests` Collection (Modified `mongodb/schema/Request.js`)

- **Remove:** `hostResponses: [HostResponseSchema]` embedded array.
- **Remove:** `acceptedResponse` embedded sub-document.
- **Add:** `acceptedResponseId: { type: mongoose.Schema.Types.ObjectId, ref: 'Response', default: null }` (to link to the chosen `Response` document).
- **Refine:** `status` field to more accurately reflect the request lifecycle (e.g., "pending_responses", "awaiting_acceptance", "confirmed", "completed", "cancelled", "expired").
- **Remove:** `isActive: { type: Boolean, default: true }` (its role will be covered by the enhanced `status` field).
- **Optional Add:** `responseCount: { type: Number, default: 0 }` (for a denormalized count of responses).

#### B. New `Responses` Collection

- **Purpose:** Each document will represent a single host's response to a request.
- **Schema Fields:**
  - `_id: mongoose.Schema.Types.ObjectId`
  - `request: { type: mongoose.Schema.Types.ObjectId, ref: 'Request', required: true, index: true }` (Link to the parent `Request`)
  - `host: { type: mongoose.Schema.Types.ObjectId, ref: 'users', required: true, index: true }` (Link to the responding host)
  - `dish: { type: mongoose.Schema.Types.ObjectId, ref: 'Dish', required: true, index: true }` (Crucially, this links to the unique `Dish` document created specifically for this response)
  - `message: { type: String }` (Host's optional message)
  - `status: { type: String, enum: ['pending_user_action', 'accepted_by_user', 'rejected_by_user', 'withdrawn_by_host'], default: 'pending_user_action' }`
  - `timestamps: true`

#### C. `Dishes` Collection (Utilizing existing `mongodb/schema/Dish.js` with considerations)

- **Workflow Clarification:** A **new `Dish` document is created by the host for every response they make.**
- This `Dish` document will contain all specific details for that offer: name, description, ingredients, and the **price for that particular offer**.
- The `dish` field in the `Response` schema will reference the `_id` of this newly created, response-specific `Dish`.
- **Optional Schema Additions for Traceability (to `Dish.js`):**
  - `isResponseSpecific: { type: Boolean, default: false }` (to differentiate from a host's regular, reusable menu items, if any)
  - `originalRequestId: { type: mongoose.Schema.Types.ObjectId, ref: 'Request' }` (linking back to the `Request` this `Dish` was created for)

### II. Mermaid Diagram of Proposed Data Model

```mermaid
erDiagram
  users ||--o{ requests : "creates_request"
  users ||--o{ dishes : "creates_dish_for_response (as_host)"
  users ||--o{ responses : "submits_response (as_host)"

  requests {
    ObjectId _id PK
    ObjectId userId FK "User who made the request"
    object dishRequestDetails "Original user's dish criteria"
    ObjectId locationId FK "Address of the request"
    string status "pending_responses, awaiting_acceptance, confirmed, etc."
    ObjectId acceptedResponseId FK "Nullable, points to the accepted Response"
    integer responseCount "Denormalized count of responses"
    datetime expiresAt
    datetime createdAt
    datetime updatedAt
  }

  responses {
    ObjectId _id PK
    ObjectId requestId FK "The Request this response is for"
    ObjectId hostId FK "User (host) who submitted this response"
    ObjectId dishId FK "The unique Dish created specifically for this response"
    string message "Optional message from host"
    string status "pending_user_action, accepted_by_user, etc."
    datetime createdAt
    datetime updatedAt
  }

  dishes {
    ObjectId _id PK
    string name
    string description
    float price "Price for this specific offer"
    ObjectId hostId FK "Host who created this dish"
    boolean isResponseSpecific "Optional: true if created for a response"
    ObjectId originalRequestId "Optional: links back to the request"
    datetime createdAt
    datetime updatedAt
  }

  addresses {
    ObjectId _id PK
    string street
    string city
  }

  requests }o--|| addresses : "located_at"
  requests ||--o{ responses : "receives"
  responses ||--|{ dishes : "offers_specific_dish"
```

### III. Logic & Controller Adjustments (Affecting `controllers/requestController.js`, `services/requestService.js`, and requiring a new `services/responseService.js`)

- **Host Responding to a Request:**
  1.  Host defines details for their offer.
  2.  A new `Dish` document is created and saved with these details (including price).
  3.  A new `Response` document is created in the `responses` collection, linking to the parent `Request`, the `Host`, and the newly created `Dish`.
  4.  The user who made the request is notified.
- **User Accepting a Response:**
  1.  The `status` of the chosen `Response` document is updated (e.g., to "accepted_by_user").
  2.  The parent `Request` document's `status` is updated (e.g., to "confirmed") and `acceptedResponseId` is set to the `_id` of the accepted `Response`.
  3.  The host whose response was accepted is notified.
  4.  This action would typically trigger the next stage (e.g., cart processing or order creation), using the details from the accepted `Response` and its associated unique `Dish`.
- **Data Fetching:**
  - Endpoints for fetching feeds, user requests, host responses, and responses for a specific request will query the appropriate collections based on this new structure.
  - Fetching host-specific responses becomes significantly more efficient.

### IV. Route Adjustments (Affecting `routes/requestRoutes.js`)

- The endpoint for a host to submit a response (e.g., `POST /requests/:requestId/responses` or a new dedicated route) will now orchestrate the creation of both the new `Dish` and the new `Response` document.
- The endpoint for a user to accept a response (e.g., `POST /responses/:responseId/accept`) will update the `Response` and the parent `Request` as described.

### V. Architectural Benefits

- **Scalability:** Prevents oversized `Request` documents and improves overall database performance.
- **Data Integrity & Clarity:** Each response and its associated unique dish offer are clearly defined, independent, and immutable once created.
- **Maintainability:** Simplifies application logic for managing individual response states and querying related data.
- **Efficiency:** Significantly improves performance for common query patterns, such as fetching all responses submitted by a specific host.

## 5. Next Steps

Once this plan is approved, the implementation phase can begin, focusing on:

1.  Creating the new `Response` schema and modifying the `Request` and `Dish` schemas as outlined.
2.  Updating database migration scripts if applicable.
3.  Refactoring the service layer (`requestService.js`, new `responseService.js`).
4.  Updating the controller logic (`requestController.js`).
5.  Modifying the API routes (`requestRoutes.js`).
6.  Thorough testing of all affected functionalities.
