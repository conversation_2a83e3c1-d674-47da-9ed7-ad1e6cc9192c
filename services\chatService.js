// services/chatService.js
const {
  Conversation,
  Message,
  userModel,
  Order,
  Host,
} = require("../mongodb/model");
const socketManager = require("../utils/websocket");
const mongoose = require("mongoose");
const { ObjectId } = mongoose.Types;

class ChatService {
  /**
   * Create a new conversation for an order between a user and host
   * @param {string} orderId - The order ID
   * @param {string} userId - The user (customer) ID
   * @param {string} hostId - The host (food provider) user ID
   * @param {Object} metadata - Additional metadata for the conversation
   * @returns {Promise<Object>} - The created conversation
   */
  async createOrderConversation(
    orderId,
    userId,
    hostId,
    hostUser,
    metadata = {}
  ) {
    try {
      // Validate inputs
      if (!orderId || !userId || !hostId) {
        throw new Error("Order ID, user ID, and host ID are required");
      }

      // Check if a conversation already exists between the user and host
      const existingConversation = await Conversation.findOne({
        host: hostUser,
        user: userId,
      });

      if (existingConversation) {
        return existingConversation;
      }
      // Create new conversation
      const newConversation = await Conversation.create({
        user: userId,
        host: hostUser,
        order: orderId,
        hostProfile: hostId,
        metadata,
        status: "active",
      });

      // Create a system message to start the conversation
      await this.createSystemMessage(
        newConversation._id,
        "Conversation started for your order. You can chat with the host here."
      );

      // Notify participants about the new conversation
      socketManager.io.to(`user:${userId}`).emit("chat:conversation_created", {
        conversationId: newConversation._id,
        order: orderId,
      });

      socketManager.io.to(`user:${hostId}`).emit("chat:conversation_created", {
        conversationId: newConversation._id,
        order: orderId,
      });

      return newConversation;
    } catch (error) {
      console.error("Error creating order conversation:", error);
      throw error;
    }
  }

  /**
   * Create a system message in a conversation
   * @param {string} conversationId - The conversation ID
   * @param {string} content - The message content
   * @returns {Promise<Object>} - The created message
   */
  async createSystemMessage(conversationId, content) {
    try {
      // Create the system message
      const systemMessage = await Message.create({
        conversation: conversationId,
        content,
        isSystem: true,
      });

      // Update the conversation's lastMessage
      await Conversation.findByIdAndUpdate(conversationId, {
        lastMessage: systemMessage._id,
        updatedAt: new Date(),
      });

      return systemMessage;
    } catch (error) {
      console.error("Error creating system message:", error);
      throw error;
    }
  }

  /**
   * Get a conversation by ID
   * @param {string} conversationId - The conversation ID
   * @returns {Promise<Object>} - The conversation
   */
  async getConversationById(conversationId) {
    try {
      return await Conversation.findById(conversationId)
        .populate({
          path: "user",
          select: "name avatar",
        })
        .populate({
          path: "host",
          select: "name avatar",
        })
        .populate({
          path: "order",
        })
        .populate({
          path: "lastMessage",
        })
        .populate({
          path: "hostProfile",
          select: "title avatar",
        });
    } catch (error) {
      console.error("Error getting conversation:", error);
      throw error;
    }
  }

  /**
   * Get conversation by order ID
   * @param {string} orderId - The order ID
   * @returns {Promise<Object>} - The conversation
   */
  async getConversationByOrderId(orderId) {
    try {
      return await Conversation.findOne({ order: orderId })
        .populate({
          path: "user",
          select: "name avatar",
        })
        .populate({
          path: "host",
          select: "name avatar",
        })
        .populate({
          path: "lastMessage",
        })
        .populate({
          path: "hostProfile",
          select: "title avatar",
        });
    } catch (error) {
      console.error("Error getting conversation by order:", error);
      throw error;
    }
  }

  /**
   * Get all conversations for a user (customer)
   * @param {string} userId - The user ID
   * @returns {Promise<Array>} - Array of conversations
   */
  async getUserConversations(userId) {
    try {
      return await Conversation.find({
        user: userId,
        status: "active",
      })
        .populate({
          path: "host",
          select: "name avatar",
        })
        .populate({
          path: "order",
          select: "orderNumber totalPrice",
        })
        .populate({
          path: "lastMessage",
        })
        .populate({
          path: "hostProfile",
          select: "title avatar",
        })
        .sort({ updatedAt: -1 });
    } catch (error) {
      console.error("Error getting user conversations:", error);
      throw error;
    }
  }

  /**
   * Get all conversations for a host
   * @param {string} hostId - The host user ID
   * @returns {Promise<Array>} - Array of conversations
   */
  async getHostConversations(hostId) {
    try {
      console.log("hostId ============>", hostId);
      return await Conversation.find({
        host: hostId,
        status: "active",
      })
        .populate({
          path: "user",
          select: "name avatar",
        })
        .populate({
          path: "order",
          select: "orderNumber totalPrice",
        })
        .populate({
          path: "lastMessage",
        })
        .sort({ updatedAt: -1 });
    } catch (error) {
      console.error("Error getting host conversations:", error);
      throw error;
    }
  }

  /**
   * Send a message in a conversation
   * @param {string} conversationId - The conversation ID
   * @param {string} senderId - The sender's user ID
   * @param {string} content - The message content
   * @param {Array} attachments - Optional attachments
   * @returns {Promise<Object>} - The created message
   */
  async sendMessage(conversationId, senderId, content, attachments = []) {
    try {
      // Validate inputs
      if (!conversationId || !senderId || !content) {
        throw new Error("Missing required parameters");
      }

      // Check if conversation exists
      const conversation = await Conversation.findById(conversationId);
      if (!conversation) {
        throw new Error("Conversation not found");
      }

      // Check if conversation is active
      if (conversation.status !== "active") {
        throw new Error("Cannot send messages in an archived conversation");
      }

      // Check if user is a participant (either the user or the host)
      if (
        conversation.user.toString() !== senderId.toString() &&
        conversation.host.toString() !== senderId.toString()
      ) {
        throw new Error("User is not a participant in this conversation");
      }

      // Create the message
      const newMessage = await Message.create({
        conversation: conversationId,
        sender: senderId,
        content,
        attachments,
      });

      // Update the conversation's lastMessage
      await Conversation.findByIdAndUpdate(conversationId, {
        lastMessage: newMessage._id,
        updatedAt: new Date(),
      });

      // Populate sender information
      const populatedMessage = await Message.findById(newMessage._id).populate({
        path: "sender",
        select: "name avatar",
      });

      // Determine the recipient (the other participant)
      const recipientId =
        conversation.user.toString() === senderId.toString()
          ? conversation.host.toString()
          : conversation.user.toString();

      // Notify the recipient about the new message
      socketManager.io.to(`user:${recipientId}`).emit("chat:message_received", {
        conversationId,
        message: populatedMessage,
      });

      return populatedMessage;
    } catch (error) {
      console.error("Error sending message:", error);
      throw error;
    }
  }

  /**
   * Get messages for a conversation
   * @param {string} conversationId - The conversation ID
   * @param {Object} options - Pagination options
   * @returns {Promise<Array>} - Array of messages
   */
  async getConversationMessages(
    conversationId,
    options = { limit: 50, skip: 0 }
  ) {
    try {
      return await Message.find({ conversation: conversationId })
        .sort({ createdAt: 1 })
        .skip(options.skip)
        .limit(options.limit)
        .populate({
          path: "sender",
          select: "name avatar",
        });
    } catch (error) {
      console.error("Error getting conversation messages:", error);
      throw error;
    }
  }

  /**
   * Mark messages as read by a user
   * @param {string} conversationId - The conversation ID
   * @param {string} userId - The user ID
   * @returns {Promise<Object>} - Result of the operation
   */
  async markMessagesAsRead(conversationId, userId) {
    try {
      // Find messages that haven't been read by this user
      const unreadMessages = await Message.find({
        conversation: conversationId,
        "readBy.user": { $ne: userId },
        sender: { $ne: userId }, // Don't mark the user's own messages
      });

      if (unreadMessages.length === 0) {
        return { acknowledged: true, modifiedCount: 0 };
      }

      // Mark messages as read
      const result = await Message.updateMany(
        {
          conversation: conversationId,
          "readBy.user": { $ne: userId },
          sender: { $ne: userId },
        },
        {
          $addToSet: {
            readBy: {
              user: userId,
              readAt: new Date(),
            },
          },
        }
      );

      // Notify the sender that their messages have been read
      const senders = [
        ...new Set(unreadMessages.map((msg) => msg.sender.toString())),
      ];
      senders.forEach((senderId) => {
        socketManager.io.to(`user:${senderId}`).emit("chat:messages_read", {
          conversationId,
          readBy: userId,
          count: result.modifiedCount,
        });
      });

      return result;
    } catch (error) {
      console.error("Error marking messages as read:", error);
      throw error;
    }
  }

  /**
   * Get unread message count for a user
   * @param {string} userId - The user ID
   * @returns {Promise<Object>} - Unread counts by conversation
   */
  async getUnreadMessageCounts(userId) {
    try {
      // Get all conversations the user is part of (either as user or host)
      const conversations = await Conversation.find({
        $or: [{ user: userId }, { host: userId }],
        status: "active",
      });

      const conversationIds = conversations.map((conv) => conv._id);

      // Aggregate unread messages by conversation
      const unreadCounts = await Message.aggregate([
        {
          $match: {
            conversation: { $in: conversationIds },
            sender: { $ne: new ObjectId(userId) },
            "readBy.user": { $ne: new ObjectId(userId) },
          },
        },
        {
          $group: {
            _id: "$conversation",
            count: { $sum: 1 },
          },
        },
      ]);

      // Format the result
      const result = {
        total: unreadCounts.reduce((sum, item) => sum + item.count, 0),
        byConversation: {},
      };

      unreadCounts.forEach((item) => {
        result.byConversation[item._id] = item.count;
      });

      return result;
    } catch (error) {
      console.error("Error getting unread message counts:", error);
      throw error;
    }
  }

  /**
   * Archive a conversation when an order is completed or cancelled
   * @param {string} orderId - The order ID
   * @returns {Promise<Object>} - The updated conversation
   */
  async archiveConversation(orderId) {
    try {
      // Find the conversation for this order
      const conversation = await Conversation.findOne({ order: orderId });

      if (!conversation) {
        throw new Error(`No conversation found for order ${orderId}`);
      }

      // Update the conversation status to archived
      const updatedConversation = await Conversation.findByIdAndUpdate(
        conversation._id,
        { status: "archived" },
        { new: true }
      );

      // Create a system message indicating the conversation is archived
      await this.createSystemMessage(
        conversation._id,
        "This conversation has been archived because the order is completed or cancelled."
      );

      // Notify participants that the conversation is archived
      socketManager.io
        .to(`user:${conversation.user}`)
        .emit("chat:conversation_archived", {
          conversationId: conversation._id,
          order: orderId,
        });

      socketManager.io
        .to(`user:${conversation.host}`)
        .emit("chat:conversation_archived", {
          conversationId: conversation._id,
          order: orderId,
        });

      return updatedConversation;
    } catch (error) {
      console.error("Error archiving conversation:", error);
      throw error;
    }
  }
}

module.exports = new ChatService();
