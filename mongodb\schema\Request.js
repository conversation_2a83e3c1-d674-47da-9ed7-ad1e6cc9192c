const mongoose = require("mongoose");

const DishRequestSchema = new mongoose.Schema({
  title: { type: String, required: true },
  ingredients: { type: [String], required: true },
  description: { type: String, default: "" },
  offering: { type: String, enum: ["Dine In", "Take Away", "Delivery"] },
  minServing: { type: Number, default: 1, min: 1 },
  maxServing: { type: Number, default: 1, min: 1 },
  quantity: { type: Number, default: 1, min: 1 },
  offeringTime: { type: Date },
});

const RequestSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "users",
      required: true,
    },
    dishRequest: DishRequestSchema,
    status: {
      type: String,
      enum: [
        "pending_responses",
        "awaiting_acceptance",
        "confirmed",
        "completed",
        "cancelled",
        "expired",
      ],
      default: "pending_responses",
    },
    location: { type: mongoose.Schema.Types.ObjectId, ref: "Address" },
    city: { type: String },
    acceptedResponseId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Response", // Will point to the new Response model
      default: null,
    },
    responseCount: {
      type: Number,
      default: 0,
    },
    expiresAt: { type: Date }, // Optional expiration time for the request
  },
  { timestamps: true }
);

// Index for efficient querying
RequestSchema.index({ "dishRequest.offeringTime": 1 }); // Keep if still relevant for feeds
RequestSchema.index({ user: 1, status: 1 });

module.exports = RequestSchema;
