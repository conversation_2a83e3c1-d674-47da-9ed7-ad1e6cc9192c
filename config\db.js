const mongoose = require("mongoose");
require("dotenv").config();
const { MONGO_URI } = process.env;

const connectDB = async () => {
  try {
    const conn = await mongoose.connect(
      "mongodb+srv://huraira12:<EMAIL>/?retryWrites=true&w=majority&appName=FamFoodyProd"
    );
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.log(error);
    process.exit(1);
  }
};

module.exports = {
  connectDB,
};
