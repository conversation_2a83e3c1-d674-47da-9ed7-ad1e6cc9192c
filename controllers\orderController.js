// controllers/orderController.js
const { Order, userModel, Host } = require("../mongodb/model");
const stripe = require("../config/stripe");
const hostService = require("../services/hostService");
const notificationService = require("../services/notificationService");
const orderHooks = require("../hooks/orderHooks");

// Create a new order with multiple host groups
exports.createOrder = async (req, res) => {
  try {
    const { hostGroups, shippingAddress, tax = 0 } = req.body;

    if (!hostGroups || !Array.isArray(hostGroups) || hostGroups.length === 0) {
      return res.status(400).json({ msg: "Host groups are required" });
    }

    // Create a new order
    const newOrder = new Order({
      userId: req.user.id,
      hostGroups,
      shippingAddress,
      tax,
    });

    // Generate orderNumber manually if the pre-save hook isn't working
    if (!newOrder.orderNumber) {
      const date = new Date();
      const year = date.getFullYear().toString().slice(-2);
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");

      // Get count of orders for today to generate sequence
      const count =
        (await Order.countDocuments({
          createdAt: {
            $gte: new Date(date.setHours(0, 0, 0, 0)),
            $lt: new Date(date.setHours(23, 59, 59, 999)),
          },
        })) + 1;

      // Format: ORD-YYMMDD-SEQUENCE (e.g., ORD-**********)
      newOrder.orderNumber = `ORD-${year}${month}${day}-${count
        .toString()
        .padStart(3, "0")}`;
    }

    // Calculate totals
    newOrder.calculateTotals();

    // Save the order
    await newOrder.save();

    res.json(newOrder);
  } catch (err) {
    console.error("Error creating order:", err);
    res.status(500).send("Server error");
  }
};

// Get all orders for the logged-in user
exports.getUserOrders = async (req, res) => {
  try {
    const orders = await Order.find({ userId: req.user.id })
      .populate("shippingAddress")
      .populate("hostGroups.hostId", "title avatar")
      .populate("hostGroups.items.dishId")
      .sort({ createdAt: -1 });

    res.json(orders);
  } catch (err) {
    console.error("Error fetching user orders:", err);
    res.status(500).send("Server error");
  }
};

// Get a specific order by ID
exports.getOrderById = async (req, res) => {
  try {
    const order = await Order.findById(req.params.id)
      .populate("hostGroups.hostId", "title avatar")
      .populate("hostGroups.items.dishId")
      .populate("userId", "name email");

    if (!order) {
      return res.status(404).json({ msg: "Order not found" });
    }

    // Check if user is authorized (either the customer or one of the hosts)
    const isCustomer = order.userId._id.toString() === req.user.id;
    const isHost = order.hostGroups.some(
      (group) => group.hostId._id.toString() === req.user.id
    );

    if (!isCustomer && !isHost) {
      return res.status(401).json({ msg: "Not authorized" });
    }

    res.json(order);
  } catch (err) {
    console.error("Error fetching order:", err);
    res.status(500).send("Server error");
  }
};

// Get all orders for a specific host
exports.getHostOrders = async (req, res) => {
  try {
    const host = await hostService.getHostByUserId(req.user.id);
    console.log("host =============> ", host);
    if (!host) {
      return res.status(404).json({ msg: "Host not found" });
    }
    // Use the static method to get host-specific orders
    const hostOrders = await Order.getHostOrders(host._id);

    // Populate necessary fields
    const populatedOrders = await Order.populate(hostOrders, [
      { path: "userId", select: "name email" },
      { path: "hostGroup.items.dishId" },
    ]);

    res.json(populatedOrders);
  } catch (err) {
    console.error("Error fetching host orders:", err);
    res.status(500).send("Server error");
  }
};

// Update the status of a host group within an order
exports.updateHostGroupStatus = async (req, res) => {
  try {
    const { orderId, status } = req.body;
    const host = await hostService.getHostByUserId(req.user.id);
    if (!host) {
      return res.status(404).json({ msg: "Host not found" });
    }
    if (
      !["pending", "processing", "completed", "cancelled", "refunded"].includes(
        status
      )
    ) {
      return res.status(400).json({ msg: "Invalid status" });
    }

    // Find the order
    const order = await Order.findById(orderId);

    if (!order) {
      return res.status(404).json({ msg: "Order not found" });
    }

    // Find the specific host group
    const hostGroup = order.hostGroups.find(
      (group) => group.hostId.toString() === host._id.toString()
    );

    if (!hostGroup) {
      return res
        .status(404)
        .json({ msg: "Host group not found in this order" });
    }

    // Update the status
    hostGroup.status = status;
    await order.save();

    // Send notification to the customer
    await notificationService.createOrderNotification(
      order.userId.toString(),
      order._id,
      order.orderNumber,
      "order_status_changed",
      status
    );

    // Send notification to the host
    const host2 = await Host.findById(hostGroup.hostId);
    if (host2) {
      await notificationService.createHostOrderNotification(
        host2.user.toString(),
        host2._id,
        order._id,
        order.orderNumber,
        "host_order_status_changed",
        status
      );
    }

    // Check if we need to archive the chat conversation
    if (status === "completed" || status === "cancelled") {
      try {
        await orderHooks.onOrderStatusChanged(order);
      } catch (err) {
        console.error("Failed to update chat conversation for order:", err);
        // Non-critical error, don't fail the status update
      }
    }

    res.json({
      msg: "Status updated successfully",
      order: {
        _id: order._id,
        orderNumber: order.orderNumber,
        userId: order.userId,
        createdAt: order.createdAt,
        hostGroup: hostGroup,
        shippingAddress: order.shippingAddress,
        payment: order.payment,
        paymentDate: order.paymentDate,
      },
    });
  } catch (err) {
    console.error("Error updating host group status:", err);
    res.status(500).send("Server error");
  }
};

// Update the status of an order
exports.updateOrderStatus = async (req, res) => {
  try {
    const { orderId, status } = req.body;

    if (
      !["pending", "processing", "completed", "cancelled", "refunded"].includes(
        status
      )
    ) {
      return res.status(400).json({ msg: "Invalid status" });
    }

    // Find the order
    const order = await Order.findById(orderId);

    if (!order) {
      return res.status(404).json({ msg: "Order not found" });
    }

    // Update the status
    order.hostGroups.forEach((group) => {
      group.status = status;
    });
    await order.save();

    // Send notification to the customer
    await notificationService.createOrderNotification(
      order.userId.toString(),
      order._id,
      order.orderNumber,
      "order_status_changed",
      status
    );

    // Send notifications to each host
    for (const hostGroup of order.hostGroups) {
      try {
        const host = await Host.findById(hostGroup.hostId);
        if (host) {
          await notificationService.createHostOrderNotification(
            host.user.toString(),
            host._id,
            order._id,
            order.orderNumber,
            "host_order_status_changed",
            status
          );
        }
      } catch (err) {
        console.error(`Failed to notify host ${hostGroup.hostId}:`, err);
      }
    }

    // Check if we need to archive the chat conversation
    if (status === "completed" || status === "cancelled") {
      try {
        await orderHooks.onOrderStatusChanged(order);
      } catch (err) {
        console.error("Failed to update chat conversation for order:", err);
        // Non-critical error, don't fail the status update
      }
    }

    res.json({ msg: "Status updated successfully", order });
  } catch (err) {
    console.error("Error updating order status:", err);
    res.status(500).send("Server error");
  }
};

// Pay for an order with multiple host groups
exports.createPaymentIntent = async (req, res) => {
  try {
    const { orderId } = req.body;

    // Get order details from database
    const order = await Order.findById(orderId);

    if (!order) {
      return res.status(404).json({ msg: "Order not found" });
    }

    if (order.userId.toString() !== req.user.id) {
      return res.status(401).json({ msg: "User not authorized" });
    }

    // Get or create customer
    let customerId;
    const user = req.user;

    if (user.stripeCustomerId) {
      customerId = user.stripeCustomerId;
    } else {
      // Create a new customer
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.email,
      });

      customerId = customer.id;

      // Update user with Stripe customer ID
      await userModel.findByIdAndUpdate(req.user.id, {
        stripeCustomerId: customerId,
      });
    }

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(order.totalPrice * 100), // Stripe works with cents
      currency: "usd",
      customer: customerId,
      automatic_payment_methods: {
        enabled: true,
      },
      metadata: {
        orderId: order.id,
        userId: req.user.id,
        orderNumber: order.orderNumber,
      },
    });

    // Update order with payment intent ID
    await Order.findByIdAndUpdate(orderId, {
      stripePaymentIntentId: paymentIntent.id,
      stripeCustomerId: customerId,
    });

    res.json({
      clientSecret: paymentIntent.client_secret,
    });
  } catch (err) {
    console.error("Error creating payment intent:", err);
    res.status(500).send("Server error");
  }
};

// Create checkout session for an order with multiple host groups
exports.createCheckoutSession = async (req, res) => {
  try {
    const { orderId } = req.body;

    // Get order details
    const order = await Order.findById(orderId)
      .populate("hostGroups.hostId", "title")
      .populate("hostGroups.items.dishId");

    if (!order) {
      return res.status(404).json({ msg: "Order not found" });
    }

    console.log("ORDER ==========>", order);

    if (order.userId.toString() !== req.user.id) {
      return res.status(401).json({ msg: "User not authorized" });
    }

    // Format line items for Stripe
    const lineItems = [];

    // Add items from each host
    order.hostGroups.forEach((hostGroup) => {
      // Add a heading for the host
      lineItems.push({
        price_data: {
          currency: order.currency,
          product_data: {
            name: `Items from ${hostGroup.hostId.title}`,
            description: "Host",
          },
          unit_amount: 0,
        },
        quantity: 1,
      });

      // Add each item from this host
      hostGroup.items.forEach((item) => {
        lineItems.push({
          price_data: {
            currency: order.currency,
            product_data: {
              name: item.name,
              images: item.image && [item.image],
            },
            unit_amount: Math.round(item.price * 100),
          },
          quantity: item.quantity,
        });
      });

      // Add service fee for this host if applicable
      if (hostGroup.serviceFee > 0) {
        lineItems.push({
          price_data: {
            currency: order.currency,
            product_data: {
              name: `Service Fee (${hostGroup.hostId.name})`,
            },
            unit_amount: Math.round(hostGroup.serviceFee * 100),
          },
          quantity: 1,
        });
      }
    });

    // Add tax if applicable
    if (order.tax > 0) {
      lineItems.push({
        price_data: {
          currency: order.currency,
          product_data: {
            name: "Tax",
          },
          unit_amount: Math.round(order.tax * 100),
        },
        quantity: 1,
      });
    }

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      line_items: lineItems,
      mode: "payment",
      success_url: `${process.env.CLIENT_URL}/orders/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.CLIENT_URL}/orders/cancel?session_id={CHECKOUT_SESSION_ID}`,
      customer_email: req.user.email,
      metadata: {
        orderId: order.id,
        userId: req.user.id,
        orderNumber: order.orderNumber,
      },
    });

    // Update order with session ID
    await Order.findByIdAndUpdate(orderId, {
      stripeSessionId: session.id,
    });

    res.json({ url: session.url });
  } catch (err) {
    console.error("Error creating checkout session:", err);
    res.status(500).send("Server error");
  }
};

// Update order status from webhook
exports.handlePaymentSuccess = async (
  orderId,
  paymentMethod = null,
  receiptUrl = null
) => {
  try {
    console.log(
      `🔍 DEBUG: handlePaymentSuccess called for order ${orderId} with method ${paymentMethod}`
    );
    console.log(`🔍 DEBUG: Call stack trace:`, new Error().stack);

    const order = await Order.findById(orderId);

    if (!order) {
      console.error(`Order not found: ${orderId}`);
      return;
    }

    // Check if order is already paid to prevent duplicate processing
    if (order.payment === "paid") {
      console.log(
        `⚠️  DEBUG: Order ${orderId} is already marked as paid, skipping duplicate processing`
      );
      return;
    }

    // Update the main order payment status
    const updates = {
      payment: "paid",
      paymentDate: new Date(),
      paymentMethod: paymentMethod || "card",
    };

    if (receiptUrl) {
      updates.receiptUrl = receiptUrl;
    }

    // Update all host groups to processing status
    order.hostGroups.forEach((group) => {
      group.status = "processing";
    });

    // Send payment success notification to the customer
    await notificationService.createOrderNotification(
      order.userId.toString(),
      order._id,
      order.orderNumber,
      "order_payment_success"
    );

    // Send notifications to each host about the new order
    for (const hostGroup of order.hostGroups) {
      try {
        const host = await Host.findById(hostGroup.hostId);
        if (host) {
          await notificationService.createHostOrderNotification(
            host.user.toString(),
            host._id,
            order._id,
            order.orderNumber,
            "host_order_received"
          );
          // Create a chat conversation for this order
          try {
            console.log(
              `🔄 DEBUG: Calling orderHooks.onOrderCreated for host ${host._id}, order ${order._id} (inside host loop)`
            );
            await orderHooks.onOrderCreated(
              order,
              host._id,
              hostGroup,
              host.user
            );
          } catch (err) {
            console.error("Failed to create chat conversation for order:", err);
            // Non-critical error, don't fail the order creation
          }
        }
      } catch (err) {
        console.error(`Failed to notify host ${hostGroup.hostId}:`, err);
      }
    }

    // Apply all updates
    Object.assign(order, updates);
    await order.save();

    // Create a chat conversation for this order
    try {
      console.log(
        `🔄 DEBUG: Calling orderHooks.onOrderCreated for order ${order._id} (outside host loop - POTENTIAL DUPLICATE)`
      );
      await orderHooks.onOrderCreated(order);
    } catch (err) {
      console.error("Failed to create chat conversation for order:", err);
      // Non-critical error, don't fail the order update
    }

    console.log(`Order ${orderId} marked as paid`);
  } catch (err) {
    console.error(`Error updating order status for ${orderId}:`, err);
  }
};
