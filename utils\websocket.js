// utils/websocket.js - Updated with debugging and error handling
const socketIO = require("socket.io");
const jwt = require("jsonwebtoken");

class SocketManager {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> Set of socket.ids
    this.connectedHosts = new Map(); // hostId -> Set of socket.ids
  }

  // Initialize Socket.IO with the HTTP server
  initialize(server, options = {}) {
    this.io = socketIO(server, {
      cors: {
        origin: process.env.CLIENT_URL || "*",
        methods: ["GET", "POST"],
        credentials: true,
        allowedHeaders: ["Authorization", "Content-Type"],
      },
      transports: ["websocket", "polling"], // Allow both for better compatibility
      path: "/socket.io", // Explicitly set the path
      ...options,
    });

    console.log("🔌 Socket.IO server initialized");

    // Middleware for authentication
    this.io.use((socket, next) => {
      try {
        console.log("🔄 Socket authentication attempt");

        // Try to get token from multiple sources
        const authHeader = socket.handshake.headers.authorization;
        const token =
          authHeader && authHeader.startsWith("Bearer ")
            ? authHeader.substring(7)
            : socket.handshake.auth.token || socket.handshake.query.token;

        if (!token) {
          console.log("❌ No authentication token provided");
          return next(new Error("AUTH_TOKEN_REQUIRED")); // Standardized error code
        }

        // For testing purposes - DEVELOPMENT ONLY
        // This allows a specific token for easier testing in non-production environments.
        // Ensure this is NEVER exposed or guessable in production.
        if (process.env.NODE_ENV !== "production" && token === "test-token") {
          console.warn(
            "✅ Using test token - DEVELOPMENT ONLY. Ensure this is not used in production."
          );
          socket.user = { id: "test-user-id", type: "user" }; // Minimal mock user
          return next();
        }

        try {
          let jwtSecret =
            process.env.JWT_SECRET_ACCESS_TOKEN || process.env.JWT_SECRET;

          if (!jwtSecret) {
            if (process.env.NODE_ENV === "production") {
              console.error(
                "🔴 CRITICAL: JWT_SECRET_ACCESS_TOKEN or JWT_SECRET is not set in production environment. Authentication will fail."
              );
              // In a production scenario, you might want to prevent the server from starting
              // or throw an error that stops the socket initialization.
              // For now, we'll let it proceed but authentication will likely fail for real tokens.
              jwtSecret = "unsafe_default_production_secret_placeholder"; // This will cause verification to fail for valid tokens
            } else {
              console.warn(
                "⚠️ WARNING: JWT_SECRET_ACCESS_TOKEN or JWT_SECRET is not set. Using a default insecure secret for development. DO NOT USE IN PRODUCTION."
              );
              jwtSecret = "your_jwt_secret_here"; // Default for non-production if not set
            }
          }

          // It's generally not a good practice to log secrets, even partial ones.
          // console.log(`🔑 Using JWT secret: ${jwtSecret.substring(0, 3)}...`);

          const decoded = jwt.verify(token, jwtSecret);

          // Ensure user has a type property (default to 'user' if not present)
          socket.user = {
            ...decoded,
            type: decoded.type || "user", // Default to 'user' if type is not present
          };

          console.log(
            `✅ Socket authenticated: ${socket.user.id} (${socket.user.type})`
          );
          next();
        } catch (jwtError) {
          console.log(
            `❌ JWT verification failed: ${jwtError.name} - ${jwtError.message}`
          );
          // Provide more specific error codes based on jwtError.name
          if (jwtError.name === "TokenExpiredError") {
            next(new Error("AUTH_TOKEN_EXPIRED"));
          } else if (jwtError.name === "JsonWebTokenError") {
            next(new Error("AUTH_TOKEN_INVALID"));
          } else {
            next(new Error("AUTH_FAILED_INVALID_TOKEN"));
          }
        }
      } catch (error) {
        console.error("🔴 Socket authentication middleware error:", error);
        next(new Error("AUTH_MIDDLEWARE_ERROR")); // Generic middleware error
      }
    });

    // Set up connection handling
    this.io.on("connection", (socket) => {
      try {
        console.log(`🟢 New socket connected: ${socket.id}`);
        this.handleConnection(socket);
      } catch (error) {
        console.error(
          `🔴 Error during initial socket connection handling for ${socket.id}: ${error.message}`,
          error
        );
        // Attempt to emit a standardized error if the socket object is available
        if (socket && typeof socket.emit === "function") {
          socket.emit("error", {
            code: "CONNECTION_SETUP_FAILED",
            message: "Server error during connection setup.",
            details: error.message,
          });
        }
      }
    });

    // Handle errors at the IO level (e.g., transport errors)
    this.io.engine.on("connection_error", (err) => {
      console.error(
        `🔴 Socket.IO engine connection_error: Code: ${err.code}, Message: ${err.message}, Context: ${err.context}`
      );
      // err.req - the request object
      // err.code - the error code, for example 1
      // err.message - the error message, for example "Session ID unknown"
      // err.context - some additional error context
    });

    return this.io;
  }

  // Handle new socket connections
  handleConnection(socket) {
    try {
      const userId = socket.user.id;
      const userType = socket.user.type; // 'host' or 'user'

      console.log(`📝 Registering ${userType} connection for ${userId}`);

      // Store user data in socket for reconnection
      socket.data.userId = userId;
      socket.data.userType = userType;

      // Store socket connection
      if (userType === "host") {
        this.addHostSocket(userId, socket.id);
      } else {
        this.addUserSocket(userId, socket.id);
      }

      // Join user-specific room
      socket.join(`${userType}:${userId}`);
      console.log(`✅ Socket ${socket.id} joined room ${userType}:${userId}`);

      // Handle room joining manually
      socket.on("join:user", ({ userId }) => {
        if (userId) {
          socket.join(`user:${userId}`);
          console.log(
            `✅ Socket ${socket.id} joined user room: user:${userId}`
          );
        }
      });

      socket.on("join:host", ({ hostId }) => {
        if (hostId) {
          socket.join(`host:${hostId}`);
          console.log(
            `✅ Socket ${socket.id} joined host room: host:${hostId}`
          );
        }
      });

      // Set up event listeners
      this.setupSocketListeners(socket);

      // Handle disconnection
      socket.on("disconnect", (reason) => {
        console.log(`🟠 Socket disconnected: ${socket.id}, reason: ${reason}`);

        // Don't attempt to reconnect for certain disconnect reasons
        if (
          reason === "client namespace disconnect" ||
          reason === "transport close"
        ) {
          console.log(
            `Socket ${socket.id} disconnected by client, not attempting reconnection`
          );
        }

        this.handleDisconnect(socket);
      });

      // Handle explicit client disconnect request
      socket.on("client:disconnect", () => {
        console.log(`🔴 Client requested disconnect: ${socket.id}`);
        socket.disconnect(true);
      });

      // Confirm successful connection to client
      socket.emit("connection:success", {
        message: "Successfully connected to WebSocket server",
        userId,
        userType,
        socketId: socket.id,
      });
    } catch (error) {
      console.error(
        `🔴 Error in handleConnection for socket ${socket.id}: ${error.message}`,
        error
      );
      socket.emit("error", {
        code: "HANDLE_CONNECTION_ERROR",
        message: "Error processing connection details.",
        details: error.message,
      });
    }
  }

  // Set up event listeners for a socket
  setupSocketListeners(socket) {
    try {
      // Host-specific events
      if (socket.user.type === "host") {
        this.setupHostEvents(socket);
      } else {
        // User-specific events
        this.setupUserEvents(socket);
      }

      // Common events for both types
      this.setupCommonEvents(socket);
    } catch (error) {
      console.error(
        `🔴 Error setting up socket listeners for ${socket.id}: ${error.message}`,
        error
      );
      // Emitting an error here might be too late if connection:success was already sent.
      // However, if critical, it could be:
      // socket.emit("error", {
      //   code: "LISTENER_SETUP_FAILED",
      //   message: "Failed to set up all event listeners.",
      //   details: error.message
      // });
    }
  }

  // Rest of your methods remain the same...
  setupHostEvents(socket) {
    socket.on("host:register", (data) => {
      console.log(`📡 Host register event from ${socket.id}`, data);
      this.handleHostRegistration(socket, data);
    });

    socket.on("request:respond", (data) => {
      console.log(`📡 Host response event from ${socket.id}`, data);
      this.handleHostResponse(socket, data);
    });
  }

  setupUserEvents(socket) {
    socket.on("request:create", (data) => {
      console.log(`📡 Create request event from ${socket.id}`, data);
      this.handleNewRequest(socket, data);
    });

    socket.on("response:accept", (data) => {
      console.log(`📡 Accept response event from ${socket.id}`, data);
      this.handleResponseAcceptance(socket, data);
    });
  }

  setupCommonEvents(socket) {
    socket.on("message", (data) => {
      console.log(`📡 Message event from ${socket.id}`, data);
      this.handleMessage(socket, data);
    });

    // Add a ping/pong for connection testing
    socket.on("ping", () => {
      socket.emit("pong", { time: new Date().toISOString() });
    });

    // Notification events
    socket.on("notification:read", (data) => {
      console.log(`📡 Notification read event from ${socket.id}`, data);
      this.handleNotificationRead(socket, data);
    });

    socket.on("notification:readAll", () => {
      console.log(`📡 Read all notifications event from ${socket.id}`);
      this.handleReadAllNotifications(socket);
    });

    // Chat events
    socket.on("chat:join_conversation", (data) => {
      console.log(`📡 Join conversation event from ${socket.id}`, data);
      this.handleJoinConversation(socket, data);
    });

    socket.on("chat:leave_conversation", (data) => {
      console.log(`📡 Leave conversation event from ${socket.id}`, data);
      this.handleLeaveConversation(socket, data);
    });

    socket.on("chat:send_message", (data) => {
      console.log(`📡 Send chat message event from ${socket.id}`, data);
      this.handleChatMessage(socket, data);
    });

    socket.on("chat:typing", (data) => {
      console.log(`📡 Typing event from ${socket.id}`, data);
      this.handleTypingEvent(socket, data);
    });

    socket.on("chat:read_messages", (data) => {
      console.log(`📡 Read messages event from ${socket.id}`, data);
      this.handleReadMessages(socket, data);
    });
  }

  // Handler functions
  async handleHostRegistration(socket, data) {
    try {
      const { location } = data;

      // Join location-based room
      if (location) {
        socket.join(`location:${location}`);
        console.log(
          `✅ Socket ${socket.id} joined location room: location:${location}`
        );
      }

      // For testing/debugging, emit success event
      socket.emit("host:registered", { success: true, location });
    } catch (error) {
      console.error(
        `🔴 Error in handleHostRegistration for socket ${socket.id}: ${error.message}`,
        error
      );
      socket.emit("error", {
        code: "HOST_REGISTRATION_FAILED",
        message: "Failed to register host.",
        details: error.message,
      });
    }
  }

  async handleMessage(socket, data) {
    try {
      console.log(`📝 Message from ${socket.id}:`, data);

      // Echo the message back with timestamp for testing
      socket.emit("message:received", {
        ...data,
        receivedAt: new Date().toISOString(),
      });
    } catch (error) {
      console.error(
        `🔴 Error handling message for socket ${socket.id}: ${error.message}`,
        error
      );
      socket.emit("error", {
        code: "MESSAGE_PROCESSING_ERROR",
        message: "Error processing message.",
        details: error.message,
      });
    }
  }

  async handleHostResponse(socket, data) {
    try {
      const { requestId, response } = data || {}; // Ensure data is not null/undefined

      if (!requestId) {
        return socket.emit("error", {
          code: "VALIDATION_ERROR",
          message: "Request ID is required for host response.",
        });
      }
      if (!response || typeof response !== "object") {
        return socket.emit("error", {
          code: "VALIDATION_ERROR",
          message: "Response object is required for host response.",
        });
      }
      if (!response.userId) {
        return socket.emit("error", {
          code: "VALIDATION_ERROR",
          message: "User ID in response object is required for host response.",
        });
      }

      // Validate and save response (Placeholder for actual validation/saving logic)
      // For example, you might check response content, structure, etc.

      // Emit to relevant users
      this.io.to(`user:${response.userId}`).emit("request:newResponse", {
        requestId,
        response, // Assuming response is the data to be sent
      });
    } catch (error) {
      console.error(
        `🔴 Error in handleHostResponse for socket ${socket.id} (RequestId: ${data?.requestId}): ${error.message}`,
        error
      );
      socket.emit("error", {
        code: "HOST_RESPONSE_FAILED",
        message: "Failed to handle host response.",
        details: error.message,
      });
    }
  }

  async handleNewRequest(socket, data) {
    try {
      // Broadcast new request to relevant hosts
      if (data.location) {
        this.io.to(`location:${data.location}`).emit("feed:newRequest", data);
      } else {
        // Broadcast to all hosts if no specific location
        this.io.emit("feed:newRequest", data);
      }
    } catch (error) {
      console.error(
        `🔴 Error in handleNewRequest for socket ${socket.id}: ${error.message}`,
        error
      );
      socket.emit("error", {
        code: "NEW_REQUEST_FAILED",
        message: "Failed to handle new request.",
        details: error.message,
      });
    }
  }

  async handleResponseAcceptance(socket, data) {
    try {
      const { requestId } = data;

      // Update request status and notify relevant parties
      this.io.emit("feed:requestInactive", { requestId });
    } catch (error) {
      console.error(
        `🔴 Error in handleResponseAcceptance for socket ${socket.id}: ${error.message}`,
        error
      );
      socket.emit("error", {
        code: "RESPONSE_ACCEPTANCE_FAILED",
        message: "Failed to handle response acceptance.",
        details: error.message,
      });
    }
  }

  // Chat handler functions
  async handleJoinConversation(socket, data) {
    try {
      const { conversationId } = data;

      if (!conversationId) {
        return socket.emit("error", {
          code: "VALIDATION_ERROR",
          message: "Conversation ID is required to join conversation.",
        });
      }

      // Join the conversation room
      socket.join(`conversation:${conversationId}`);
      console.log(
        `✅ Socket ${socket.id} joined conversation room: conversation:${conversationId}`
      );

      // Notify the user that they've joined successfully
      socket.emit("chat:joined_conversation", {
        conversationId,
        success: true,
      });
    } catch (error) {
      console.error(
        `🔴 Error joining conversation for socket ${socket.id}: ${error.message}`,
        error
      );
      socket.emit("error", {
        code: "JOIN_CONVERSATION_FAILED",
        message: "Error joining conversation.",
        details: error.message,
      });
    }
  }

  async handleLeaveConversation(socket, data) {
    try {
      const { conversationId } = data;

      if (!conversationId) {
        return socket.emit("error", {
          code: "VALIDATION_ERROR",
          message: "Conversation ID is required to leave conversation.",
        });
      }

      // Leave the conversation room
      socket.leave(`conversation:${conversationId}`);
      console.log(
        `✅ Socket ${socket.id} left conversation room: conversation:${conversationId}`
      );

      // Notify the user that they've left successfully
      socket.emit("chat:left_conversation", {
        conversationId,
        success: true,
      });
    } catch (error) {
      console.error(
        `🔴 Error leaving conversation for socket ${socket.id}: ${error.message}`,
        error
      );
      socket.emit("error", {
        code: "LEAVE_CONVERSATION_FAILED",
        message: "Error leaving conversation.",
        details: error.message,
      });
    }
  }

  async handleChatMessage(socket, data) {
    try {
      const { conversationId, content, attachments } = data || {}; // Ensure data is not null/undefined
      const senderId = socket.user.id; // Assuming socket.user is populated by auth middleware

      if (!conversationId) {
        return socket.emit("error", {
          code: "VALIDATION_ERROR",
          message: "Conversation ID is required to send a message.",
        });
      }
      if (!content || typeof content !== "string" || content.trim() === "") {
        return socket.emit("error", {
          code: "VALIDATION_ERROR",
          message: "Non-empty message content is required to send a message.",
        });
      }
      // Optional: Validate attachments if necessary (e.g., type, size, count)
      if (attachments && !Array.isArray(attachments)) {
        return socket.emit("error", {
          code: "VALIDATION_ERROR",
          message: "Attachments must be an array if provided.",
        });
      }

      // Use the chat service to save the message
      const chatService = require("../services/chatService");
      const message = await chatService.sendMessage(
        conversationId,
        senderId,
        content,
        attachments
      );

      // The chat service will handle broadcasting to other participants
      // But we'll also acknowledge receipt to the sender
      socket.emit("chat:message_sent", {
        conversationId,
        messageId: message._id, // Assuming message object has _id
        success: true,
      });
    } catch (error) {
      console.error(
        `🔴 Error sending chat message for socket ${socket.id} (Conversation: ${data?.conversationId}): ${error.message}`,
        error
      );
      // Check if the error is from the chatService and potentially has more specific info
      if (error.isChatServiceError) {
        // Example: if chatService throws custom errors
        socket.emit("error", {
          code: error.code || "CHAT_SERVICE_ERROR",
          message: error.message || "Failed to send message via chat service.",
          details: error.details,
        });
      } else {
        socket.emit("error", {
          code: "SEND_MESSAGE_FAILED",
          message: "Error sending message.",
          details: error.message,
        });
      }
    }
  }

  async handleTypingEvent(socket, data) {
    try {
      const { conversationId, isTyping } = data;
      const userId = socket.user.id;
      const userName = socket.user.name || "User";

      if (!conversationId) {
        return socket.emit("error", {
          code: "VALIDATION_ERROR",
          message: "Conversation ID is required for typing event.",
        });
      }

      // Broadcast typing status to other participants in the conversation
      socket.to(`conversation:${conversationId}`).emit("chat:user_typing", {
        conversationId,
        userId,
        userName,
        isTyping: isTyping === true, // Ensure boolean
      });
    } catch (error) {
      console.error(
        `🔴 Error handling typing event for socket ${socket.id}: ${error.message}`,
        error
      );
      socket.emit("error", {
        code: "TYPING_EVENT_FAILED",
        message: "Error processing typing event.",
        details: error.message,
      });
    }
  }

  async handleReadMessages(socket, data) {
    try {
      const { conversationId } = data;
      const userId = socket.user.id;

      if (!conversationId) {
        return socket.emit("error", {
          code: "VALIDATION_ERROR",
          message: "Conversation ID is required to mark messages as read.",
        });
      }

      // Use the chat service to mark messages as read
      const chatService = require("../services/chatService");
      const result = await chatService.markMessagesAsRead(
        conversationId,
        userId
      );

      // The chat service will handle notifying other participants
      // But we'll also acknowledge to the user
      socket.emit("chat:messages_marked_read", {
        conversationId,
        success: true,
        count: result.modifiedCount,
      });
    } catch (error) {
      console.error(
        `🔴 Error marking messages as read for socket ${socket.id}: ${error.message}`,
        error
      );
      socket.emit("error", {
        code: "MARK_READ_FAILED",
        message: "Error marking messages as read.",
        details: error.message,
      });
    }
  }

  /**
   * Handle notification read event
   * @param {Object} socket - Socket instance
   * @param {Object} data - Event data
   */
  async handleNotificationRead(socket, data) {
    try {
      const { notificationId } = data;
      const userId = socket.user.id;

      if (!notificationId) {
        return socket.emit("error", {
          code: "VALIDATION_ERROR",
          message: "Notification ID is required to mark notification as read.",
        });
      }

      // Use the notification service to mark as read
      const notificationService = require("../services/notificationService");
      const notification = await notificationService.markAsRead(
        notificationId,
        userId
      );
      if (!notification) {
        return socket.emit("error", {
          code: "NOTIFICATION_NOT_FOUND",
          message: "Notification not found or already read.",
        });
      }
      socket.emit("notification:readAck", { notificationId });
    } catch (error) {
      console.error(
        `🔴 Error handling notification read for socket ${socket.id}: ${error.message}`,
        error
      );
      socket.emit("error", {
        code: "NOTIFICATION_READ_FAILED",
        message: "Failed to handle notification read.",
        details: error.message,
      });
    }
  }

  /**
   * Handle read all notifications event
   * @param {Object} socket - Socket instance
   */
  async handleReadAllNotifications(socket) {
    try {
      const userId = socket.user.id;
      // Use the notification service to mark all as read
      const notificationService = require("../services/notificationService");
      const result = await notificationService.markAllAsRead(userId);
      if (result.modifiedCount === 0) {
        return socket.emit("error", {
          code: "NO_UNREAD_NOTIFICATIONS",
          message: "No unread notifications to mark as read.",
        });
      }
      socket.emit("notification:readAllAck", { success: true });
    } catch (error) {
      console.error(
        `🔴 Error handling read all notifications: ${error.message}`
      );
      socket.emit("error", {
        code: "NOTIFICATION_READALL_FAILED",
        message: "Failed to handle read all notifications.",
        details: error.message,
      });
    }
  }
  // User connection management methods remain the same
  addUserSocket(userId, socketId) {
    if (!this.connectedUsers.has(userId)) {
      this.connectedUsers.set(userId, new Set());
    }
    this.connectedUsers.get(userId).add(socketId);
    console.log(`👤 User socket added: ${userId} -> ${socketId}`);
    console.log(`👥 Connected users: ${this.connectedUsers.size}`);
  }

  removeUserSocket(userId, socketId) {
    const userSockets = this.connectedUsers.get(userId);
    if (userSockets) {
      userSockets.delete(socketId);
      console.log(`👤 User socket removed: ${userId} -> ${socketId}`);
      if (userSockets.size === 0) {
        this.connectedUsers.delete(userId);
        console.log(`👤 User removed from connected users: ${userId}`);
      }
      console.log(`👥 Connected users: ${this.connectedUsers.size}`);
    }
  }

  // Host connection management methods remain the same
  addHostSocket(hostId, socketId) {
    if (!this.connectedHosts.has(hostId)) {
      this.connectedHosts.set(hostId, new Set());
    }
    this.connectedHosts.get(hostId).add(socketId);
    console.log(`🏠 Host socket added: ${hostId} -> ${socketId}`);
    console.log(`🏘️ Connected hosts: ${this.connectedHosts.size}`);
  }

  removeHostSocket(hostId, socketId) {
    const hostSockets = this.connectedHosts.get(hostId);
    if (hostSockets) {
      hostSockets.delete(socketId);
      console.log(`🏠 Host socket removed: ${hostId} -> ${socketId}`);
      if (hostSockets.size === 0) {
        this.connectedHosts.delete(hostId);
        console.log(`🏠 Host removed from connected hosts: ${hostId}`);
      }
      console.log(`🏘️ Connected hosts: ${this.connectedHosts.size}`);
    }
  }

  // Handle socket disconnection
  handleDisconnect(socket) {
    try {
      if (!socket.user) {
        console.log(
          `⚠️ Socket ${socket.id} disconnected without user information (likely before full auth, if auth failed, or if socket.user was not set).`
        );
        return;
      }

      const userId = socket.user.id;
      const userType = socket.user.type;

      if (userType === "host") {
        this.removeHostSocket(userId, socket.id);
      } else {
        this.removeUserSocket(userId, socket.id);
      }
    } catch (error) {
      console.error(
        `🔴 Error handling disconnect for socket ${socket.id} (User: ${socket.user?.id}, Type: ${socket.user?.type}): ${error.message}`,
        error
      );
    }
  }

  // Broadcast methods
  broadcastToHosts(event, data, filter = null) {
    try {
      let targets = Array.from(this.connectedHosts.keys());
      if (filter) {
        targets = targets.filter(filter);
      }

      console.log(`📢 Broadcasting to ${targets.length} hosts: ${event}`);

      targets.forEach((hostId) => {
        const sockets = this.connectedHosts.get(hostId);
        if (sockets) {
          sockets.forEach((socketId) => {
            this.io.to(socketId).emit(event, data);
          });
        }
      });
    } catch (error) {
      console.error(`🔴 Error broadcasting to hosts: ${error.message}`);
    }
  }

  broadcastToUsers(event, data, filter = null) {
    try {
      let targets = Array.from(this.connectedUsers.keys());
      if (filter) {
        targets = targets.filter(filter);
      }

      console.log(`📢 Broadcasting to ${targets.length} users: ${event}`);

      targets.forEach((userId) => {
        const sockets = this.connectedUsers.get(userId);
        if (sockets) {
          sockets.forEach((socketId) => {
            this.io.to(socketId).emit(event, data);
          });
        }
      });
    } catch (error) {
      console.error(`🔴 Error broadcasting to users: ${error.message}`);
    }
  }
}

module.exports = new SocketManager();
