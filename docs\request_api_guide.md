# Eats Express - Request API Guide

This document outlines the HTTP API endpoints for managing dish requests and responses.

**Authentication:** All endpoints require a valid JWT token passed in the `Authorization` header as a Bearer token (e.g., `Authorization: Bearer <token>`). This is handled by the `verifyToken` middleware.

---

## Requests

### 1. Create Request

- **HTTP Method:** `POST`
- **Path:** `/requests/`
- **Description:** Creates a new dish request.
- **Authentication:** Required.
- **Request Body Example:**
  ```json
  {
    "location": {
      "street": "123 Main St",
      "city": "Anytown",
      "state": "CA",
      "zipCode": "90210",
      "country": "USA",
      "coordinates": [-118.4004, 33.9207]
    },
    "title": "Craving Homemade Lasagna",
    "ingredients": ["Pasta", "Ground Beef", "Tomato Sauce", "Cheese"],
    "description": "Looking for a classic beef lasagna, enough for 2 people.",
    "offering": "Delivery",
    "minServing": 2,
    "maxServing": 2,
    "quantity": 1,
    "offeringTime": "2025-07-15T18:00:00.000Z"
  }
  ```
- **Response Payload Example (201 Created):**
  ```json
  {
    "_id": "60d0fe4f5311236168a109cc",
    "user": "60d0fe4f5311236168a109c0",
    "dishRequest": {
      "title": "Craving Homemade Lasagna",
      "ingredients": ["Pasta", "Ground Beef", "Tomato Sauce", "Cheese"],
      "description": "Looking for a classic beef lasagna, enough for 2 people.",
      "offering": "Delivery",
      "minServing": 2,
      "maxServing": 2,
      "quantity": 1,
      "offeringTime": "2025-07-15T18:00:00.000Z"
    },
    "status": "pending_responses",
    "location": "60d0fe4f5311236168a109cb",
    "city": "Anytown",
    "responseCount": 0,
    "expiresAt": null,
    "createdAt": "2025-06-17T10:00:00.000Z",
    "updatedAt": "2025-06-17T10:00:00.000Z"
  }
  ```

---

### 2. Get Request Feed

- **HTTP Method:** `GET`
- **Path:** `/requests/feed`
- **Description:** Retrieves a feed of active dish requests, optionally filtered by location. Supports pagination.
- **Authentication:** Required.
- **Query Parameters:**
  - `location` (optional, string): City name to filter requests.
  - `page` (optional, number, default: 1): Page number for pagination.
  - `limit` (optional, number, default: 20): Number of items per page.
- **Response Payload Example (200 OK):**
  ```json
  {
    "requests": [
      {
        "_id": "60d0fe4f5311236168a109cc",
        "user": {
          "_id": "60d0fe4f5311236168a109c0",
          "name": "Requesting User"
        },
        "dishRequest": {
          "title": "Craving Homemade Lasagna",
          "offering": "Delivery",
          "quantity": 1,
          "offeringTime": "2025-07-15T18:00:00.000Z"
        },
        "status": "pending_responses",
        "location": {
          "_id": "60d0fe4f5311236168a109cb",
          "city": "Anytown",
          "street": "123 Main St"
        },
        "city": "Anytown",
        "responseCount": 0,
        "createdAt": "2025-06-17T10:00:00.000Z"
      }
    ],
    "totalPages": 5,
    "currentPage": 1,
    "totalRequests": 95
  }
  ```

---

### 3. Get User's Requests

- **HTTP Method:** `GET`
- **Path:** `/requests/user`
- **Description:** Retrieves all requests created by the authenticated user. Supports filtering by status and pagination.
- **Authentication:** Required.
- **Query Parameters:**
  - `status` (optional, string): Filter by request status (e.g., "pending_responses", "confirmed").
  - `page` (optional, number, default: 1): Page number.
  - `limit` (optional, number, default: 20): Items per page.
- **Response Payload Example (200 OK):**
  ```json
  {
    "requests": [
      {
        "_id": "60d0fe4f5311236168a109cc",
        "user": "60d0fe4f5311236168a109c0",
        "dishRequest": {
          "title": "Craving Homemade Lasagna",
          "offering": "Delivery",
          "quantity": 1,
          "offeringTime": "2025-07-15T18:00:00.000Z"
        },
        "status": "pending_responses",
        "location": "60d0fe4f5311236168a109cb",
        "city": "Anytown",
        "responseCount": 0,
        "createdAt": "2025-06-17T10:00:00.000Z"
      }
    ],
    "totalPages": 1,
    "currentPage": 1,
    "totalRequests": 10
  }
  ```

---

### 4. Get Request by ID

- **HTTP Method:** `GET`
- **Path:** `/requests/:requestId`
- **Description:** Retrieves a specific dish request by its ID.
- **Authentication:** Required.
- **Path Parameters:**
  - `requestId` (string, required): The ID of the request.
- **Response Payload Example (200 OK):**
  ```json
  {
    "_id": "60d0fe4f5311236168a109cc",
    "user": { "_id": "60d0fe4f5311236168a109c0", "name": "Requesting User" },
    "dishRequest": {
      "title": "Craving Homemade Lasagna",
      "ingredients": ["Pasta", "Ground Beef", "Tomato Sauce", "Cheese"],
      "description": "Looking for a classic beef lasagna, enough for 2 people.",
      "offering": "Delivery",
      "minServing": 2,
      "maxServing": 2,
      "quantity": 1,
      "offeringTime": "2025-07-15T18:00:00.000Z"
    },
    "status": "pending_responses",
    "location": {
      "_id": "60d0fe4f5311236168a109cb",
      "city": "Anytown",
      "street": "123 Main St"
    },
    "city": "Anytown",
    "responseCount": 0,
    "expiresAt": null,
    "createdAt": "2025-06-17T10:00:00.000Z",
    "updatedAt": "2025-06-17T10:00:00.000Z"
  }
  ```

---

## Responses (to Requests)

### 5. Get Host's Responses

- **HTTP Method:** `GET`
- **Path:** `/requests/host-responses`
- **Description:** Retrieves all responses made by the authenticated host. Supports filtering by status and pagination.
- **Authentication:** Required (user must be a host).
- **Query Parameters:**
  - `status` (optional, string): Filter by response status (e.g., "pending_user_action", "accepted_by_user").
  - `page` (optional, number, default: 1).
  - `limit` (optional, number, default: 20).
- **Response Payload Example (200 OK):**
  ```json
  {
    "responses": [
      {
        "_id": "60d0fe4f5311236168a109dd",
        "request": {
          "_id": "60d0fe4f5311236168a109cc",
          "dishRequest": { "title": "Craving Homemade Lasagna" }
        },
        "host": "60d0fe4f5311236168a109c1",
        "dish": {
          "_id": "60d0fe4f5311236168a109de",
          "name": "My Famous Lasagna",
          "price": 15
        },
        "message": "I can make this for you! My lasagna is a local favorite.",
        "status": "pending_user_action",
        "createdAt": "2025-06-17T10:05:00.000Z"
      }
    ],
    "totalPages": 2,
    "currentPage": 1,
    "totalResponses": 30
  }
  ```

---

### 6. Get Responses for a Request

- **HTTP Method:** `GET`
- **Path:** `/requests/:requestId/responses`
- **Description:** Retrieves all responses submitted for a specific dish request. Supports pagination.
- **Authentication:** Required.
- **Path Parameters:**
  - `requestId` (string, required): The ID of the request.
- **Query Parameters:**
  - `page` (optional, number, default: 1).
  - `limit` (optional, number, default: 20).
- **Response Payload Example (200 OK):**
  ```json
  {
    "responses": [
      {
        "_id": "60d0fe4f5311236168a109dd",
        "request": "60d0fe4f5311236168a109cc",
        "host": { "_id": "60d0fe4f5311236168a109c1", "name": "Host User" },
        "dish": {
          "_id": "60d0fe4f5311236168a109de",
          "name": "My Famous Lasagna",
          "price": 15
        },
        "message": "I can make this for you! My lasagna is a local favorite.",
        "status": "pending_user_action",
        "createdAt": "2025-06-17T10:05:00.000Z"
      }
    ],
    "totalPages": 1,
    "currentPage": 1,
    "totalResponses": 5
  }
  ```

---

### 7. Respond to a Request

- **HTTP Method:** `POST`
- **Path:** `/requests/:requestId/responses`
- **Description:** Allows a host to submit a response (an offer) to a specific dish request. This creates a new Dish specific to this response.
- **Authentication:** Required (user must be a host).
- **Path Parameters:**
  - `requestId` (string, required): The ID of the request to respond to.
- **Request Body Example:**
  ```json
  {
    "message": "I can prepare this dish for you! It's one of my specialties.",
    "name": "Special Offer: Homemade Lasagna",
    "price": 18.0,
    "description": "Authentic Italian lasagna made with fresh ingredients, served with a side of garlic bread.",
    "photos": ["url_to_photo1.jpg", "url_to_photo2.jpg"],
    "ingredients": [
      "Pasta",
      "Ground Beef",
      "Bechamel Sauce",
      "Parmesan Cheese",
      "Tomato Sauce"
    ],
    "cuisine": "60d0fe4f5311236168a109c2",
    "minServings": 2,
    "maxServings": 4,
    "availability": [
      {
        "date": "2025-07-16",
        "startTime": "17:00",
        "endTime": "20:00"
      }
    ],
    "offering": ["Delivery", "Take Away"]
  }
  ```
- **Response Payload Example (201 Created):**
  ```json
  {
    "_id": "60d0fe4f5311236168a109dd",
    "request": "60d0fe4f5311236168a109cc",
    "host": "60d0fe4f5311236168a109c1",
    "dish": "60d0fe4f5311236168a109de",
    "message": "I can prepare this dish for you! It's one of my specialties.",
    "status": "pending_user_action",
    "createdAt": "2025-06-17T10:05:00.000Z",
    "updatedAt": "2025-06-17T10:05:00.000Z"
  }
  ```

---

### 8. Accept a Response

- **HTTP Method:** `POST`
- **Path:** `/requests/:requestId/responses/:responseId/accept`
- **Description:** Allows the user who created the request to accept a specific response from a host.
- **Authentication:** Required (user must be the creator of the request).
- **Path Parameters:**
  - `requestId` (string, required): The ID of the original request.
  - `responseId` (string, required): The ID of the response to accept.
- **Request Body Example:** (Empty)
- **Response Payload Example (200 OK):**
  ```json
  {
    "_id": "60d0fe4f5311236168a109cc",
    "user": "60d0fe4f5311236168a109c0",
    "dishRequest": {
      "title": "Craving Homemade Lasagna"
    },
    "status": "confirmed",
    "location": "60d0fe4f5311236168a109cb",
    "city": "Anytown",
    "acceptedResponseId": {
      "_id": "60d0fe4f5311236168a109dd",
      "host": { "_id": "60d0fe4f5311236168a109c1", "name": "Host User" },
      "dish": {
        "_id": "60d0fe4f5311236168a109de",
        "name": "Special Offer: Homemade Lasagna"
      },
      "message": "I can prepare this dish for you! It's one of my specialties.",
      "status": "accepted_by_user"
    },
    "responseCount": 3,
    "createdAt": "2025-06-17T10:00:00.000Z",
    "updatedAt": "2025-06-17T10:15:00.000Z"
  }
  ```
