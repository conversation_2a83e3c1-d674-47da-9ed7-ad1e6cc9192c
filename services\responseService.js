const { Response } = require("../mongodb/model"); // Adjust path as necessary
const RequestService = require("./requestService");
// const Request = require("../mongodb/schema/Request"); // May be needed for some operations
// const Dish = require("../mongodb/schema/Dish"); // May be needed

class ResponseService {
  constructor() {}

  /**
   * Creates a new response for a request.
   * @param {Object} responseData - Data for the new response.
   * @param {string} responseData.request - The ID of the request.
   * @param {string} responseData.host - The ID of the host.
   *   @param {string} responseData.dish - The ID of the dish created for this response.
   * @param {string} [responseData.message] - Optional message from the host.
   * @returns {Promise<Document>} The created response document.
   */
  async createResponse(responseData) {
    try {
      const newResponse = new Response(responseData);
      await newResponse.save();

      return newResponse;
    } catch (error) {
      // console.error("Error creating response:", error);
      throw error;
    }
  }

  /**
   * Retrieves a response by its ID.
   * @param {string} responseId - The ID of the response.
   * @returns {Promise<Document|null>} The response document or null if not found.
   */
  async getResponseById(responseId) {
    try {
      // Consider populating related fields if needed immediately
      // e.g., .populate('request').populate('host').populate('dish');
      const response = await Response.findById(responseId);
      return response;
    } catch (error) {
      // console.error("Error fetching response by ID:", error);
      throw error;
    }
  }

  /**
   * Retrieves all responses submitted by a specific host.
   * @param {string} hostId - The ID of the host.
   * @param {object} [options] - Pagination options { page, limit }.
   * @returns {Promise<Array<Document>>} A list of responses.
   */
  async getHostResponses(hostId, options = {}) {
    try {
      const page = parseInt(options.page, 10) || 1;
      const limit = parseInt(options.limit, 10) || 20;
      const skip = (page - 1) * limit;

      const responses = await Response.find({ host: hostId })
        .populate("request", "dishRequest status") // Populate relevant fields from Request
        .populate("dish") // Populate the associated Dish
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const totalResponses = await Response.countDocuments({ host: hostId });

      return {
        data: responses,
        page,
        limit,
        totalPages: Math.ceil(totalResponses / limit),
        totalResponses,
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Retrieves all responses for a specific request.
   * @param {string} requestId - The ID of the request.
   * @param {object} [options] - Pagination options { page, limit }.
   * @returns {Promise<Array<Document>>} A list of responses.
   */
  async getResponsesForRequest(requestId, options = {}) {
    try {
      const page = parseInt(options.page, 10) || 1;
      const limit = parseInt(options.limit, 10) || 20;
      const skip = (page - 1) * limit;

      const responses = await Response.find({ request: requestId })
        .populate("host", "firstName lastName profilePicture") // Populate host details
        .populate("dish") // Populate the associated Dish
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const totalResponses = await Response.countDocuments({
        request: requestId,
      });

      return {
        data: responses,
        page,
        limit,
        totalPages: Math.ceil(totalResponses / limit),
        totalResponses,
      };
    } catch (error) {
      throw error;
    }
  }
  // Add other methods as needed, e.g., updateResponseStatus, etc.
}

module.exports = ResponseService;
