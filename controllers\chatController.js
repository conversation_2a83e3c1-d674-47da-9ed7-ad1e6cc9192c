// controllers/chatController.js
const chatService = require("../services/chatService");
const AppError = require("../utils/error");
const {
  BAD_REQUEST,
  NOT_FOUND,
  INTERNAL_SERVER,
} = require("../constants/errorCodes");

/**
 * Create a new conversation for an order
 * This is typically called automatically when an order is created
 */
exports.createOrderConversation = async (req, res, next) => {
  try {
    const { orderId, userId, hostId, metadata } = req.body;

    if (!orderId || !userId || !hostId) {
      return next(
        new AppError(
          { message: "Order ID, user ID, and host ID are required" },
          BAD_REQUEST
        )
      );
    }

    // Verify the current user is either the user or the host
    if (req.user.id !== userId && req.user.id !== hostId) {
      return next(
        new AppError(
          { message: "Unauthorized to create this conversation" },
          BAD_REQUEST
        )
      );
    }

    const conversation = await chatService.createOrderConversation(
      orderId,
      userId,
      hostId,
      metadata
    );

    res.status(201).json({
      success: true,
      data: conversation,
    });
  } catch (error) {
    console.error("Error creating order conversation:", error);
    return next(new AppError({ message: error.message }, BAD_REQUEST));
  }
};

/**
 * Get all conversations for the authenticated user
 */
exports.getUserConversations = async (req, res, next) => {
  try {
    // Determine if the user is acting as a customer or host
    const isHost = req.query.asHost === "true";

    let conversations;
    if (isHost) {
      conversations = await chatService.getHostConversations(req.user.id);
    } else {
      conversations = await chatService.getUserConversations(req.user.id);
    }

    res.status(200).json({
      success: true,
      count: conversations.length,
      data: conversations,
    });
  } catch (error) {
    console.error("Error getting user conversations:", error);
    return next(
      new AppError(
        { message: "Failed to retrieve conversations" },
        INTERNAL_SERVER
      )
    );
  }
};

/**
 * Get a conversation by ID
 */
exports.getConversationById = async (req, res, next) => {
  try {
    const { conversationId } = req.params;
    const conversation = await chatService.getConversationById(conversationId);

    if (!conversation) {
      return next(
        new AppError({ message: "Conversation not found" }, NOT_FOUND)
      );
    }
    console.log("conversation ============>", conversation);
    console.log("req.user.id ============>", req.user.id);
    // Check if user is a participant (either user or host)
    if (
      conversation.user._id.toString() !== req.user.id &&
      conversation.host._id.toString() !== req.user.id
    ) {
      return next(
        new AppError(
          { message: "You are not a participant in this conversation" },
          BAD_REQUEST
        )
      );
    }

    res.status(200).json({
      success: true,
      data: conversation,
    });
  } catch (error) {
    console.error("Error getting conversation:", error);
    return next(
      new AppError(
        { message: "Failed to retrieve conversation" },
        INTERNAL_SERVER
      )
    );
  }
};

/**
 * Get a conversation by order ID
 */
exports.getConversationByOrderId = async (req, res, next) => {
  try {
    const { orderId } = req.params;
    const conversation = await chatService.getConversationByOrderId(orderId);

    if (!conversation) {
      return next(
        new AppError(
          { message: "Conversation not found for this order" },
          NOT_FOUND
        )
      );
    }

    // Check if user is a participant (either user or host)
    if (
      conversation.user._id.toString() !== req.user.id &&
      conversation.host._id.toString() !== req.user.id
    ) {
      return next(
        new AppError(
          { message: "You are not a participant in this conversation" },
          BAD_REQUEST
        )
      );
    }

    res.status(200).json({
      success: true,
      data: conversation,
    });
  } catch (error) {
    console.error("Error getting conversation by order:", error);
    return next(
      new AppError(
        { message: "Failed to retrieve conversation" },
        INTERNAL_SERVER
      )
    );
  }
};

/**
 * Send a message in a conversation
 */
exports.sendMessage = async (req, res, next) => {
  try {
    const { conversationId } = req.params;
    const { content, attachments } = req.body;

    const message = await chatService.sendMessage(
      conversationId,
      req.user.id,
      content,
      attachments
    );

    res.status(201).json({
      success: true,
      data: message,
    });
  } catch (error) {
    console.error("Error sending message:", error);
    return next(new AppError({ message: error.message }, BAD_REQUEST));
  }
};

/**
 * Get messages for a conversation
 */
exports.getConversationMessages = async (req, res, next) => {
  try {
    const { conversationId } = req.params;
    const { limit = 50, skip = 0 } = req.query;

    // First check if the conversation exists and user is a participant
    const conversation = await chatService.getConversationById(conversationId);

    if (!conversation) {
      return next(
        new AppError({ message: "Conversation not found" }, NOT_FOUND)
      );
    }

    console.log("conversation ============>", conversation);
    console.log("req.user.id ============>", req.user.id);
    // Check if user is a participant (either user or host)
    if (
      conversation.user._id.toString() !== req.user.id &&
      conversation.host._id.toString() !== req.user.id
    ) {
      return next(
        new AppError(
          { message: "You are not a participant in this conversation" },
          BAD_REQUEST
        )
      );
    }

    const messages = await chatService.getConversationMessages(conversationId, {
      limit: parseInt(limit),
      skip: parseInt(skip),
    });

    res.status(200).json({
      success: true,
      count: messages.length,
      data: messages,
    });
  } catch (error) {
    console.error("Error getting conversation messages:", error);
    return next(
      new AppError({ message: "Failed to retrieve messages" }, INTERNAL_SERVER)
    );
  }
};

/**
 * Mark messages as read
 */
exports.markMessagesAsRead = async (req, res, next) => {
  try {
    const { conversationId } = req.params;

    // Check if the conversation exists and user is a participant
    const conversation = await chatService.getConversationById(conversationId);

    if (!conversation) {
      return next(
        new AppError({ message: "Conversation not found" }, NOT_FOUND)
      );
    }

    // Check if user is a participant (either user or host)
    if (
      conversation.user._id.toString() !== req.user.id &&
      conversation.host._id.toString() !== req.user.id
    ) {
      return next(
        new AppError(
          { message: "You are not a participant in this conversation" },
          BAD_REQUEST
        )
      );
    }

    const result = await chatService.markMessagesAsRead(
      conversationId,
      req.user.id
    );

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error marking messages as read:", error);
    return next(
      new AppError(
        { message: "Failed to mark messages as read" },
        INTERNAL_SERVER
      )
    );
  }
};

/**
 * Get unread message counts for the authenticated user
 */
exports.getUnreadMessageCounts = async (req, res, next) => {
  try {
    const unreadCounts = await chatService.getUnreadMessageCounts(req.user.id);

    res.status(200).json({
      success: true,
      data: unreadCounts,
    });
  } catch (error) {
    console.error("Error getting unread message counts:", error);
    return next(
      new AppError(
        { message: "Failed to retrieve unread message counts" },
        INTERNAL_SERVER
      )
    );
  }
};

/**
 * Archive a conversation when an order is completed or cancelled
 */
exports.archiveConversation = async (req, res, next) => {
  try {
    const { orderId } = req.params;

    if (!orderId) {
      return next(
        new AppError({ message: "Order ID is required" }, BAD_REQUEST)
      );
    }

    // First check if the conversation exists
    const conversation = await chatService.getConversationByOrderId(orderId);

    if (!conversation) {
      return next(
        new AppError(
          { message: "Conversation not found for this order" },
          NOT_FOUND
        )
      );
    }

    // Only allow the host or an admin to archive a conversation
    if (conversation.host.toString() !== req.user.id) {
      return next(
        new AppError(
          { message: "Only the host can archive this conversation" },
          BAD_REQUEST
        )
      );
    }

    const updatedConversation = await chatService.archiveConversation(orderId);

    res.status(200).json({
      success: true,
      data: updatedConversation,
    });
  } catch (error) {
    console.error("Error archiving conversation:", error);
    return next(
      new AppError(
        { message: "Failed to archive conversation" },
        INTERNAL_SERVER
      )
    );
  }
};
