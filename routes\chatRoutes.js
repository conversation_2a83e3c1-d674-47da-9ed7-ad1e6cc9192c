// routes/chatRoutes.js
const express = require("express");
const chatController = require("../controllers/chatController");
const { verifyToken } = require("../middlewares/authMiddleware");

const router = express.Router();

// Apply authentication middleware to all chat routes
router.use(verifyToken);

// Conversation routes
router.post("/order-conversations", chatController.createOrderConversation);
router.get("/conversations", chatController.getUserConversations);
router.get(
  "/conversations/:conversationId",
  chatController.getConversationById
);
router.get(
  "/order/:orderId/conversation",
  chatController.getConversationByOrderId
);

// Message routes
router.post(
  "/conversations/:conversationId/messages",
  chatController.sendMessage
);
router.get(
  "/conversations/:conversationId/messages",
  chatController.getConversationMessages
);
router.post(
  "/conversations/:conversationId/read",
  chatController.markMessagesAsRead
);

// Conversation management
router.post("/order/:orderId/archive", chatController.archiveConversation);

// Unread counts
router.get("/unread", chatController.getUnreadMessageCounts);

module.exports = router;
