const { Dish, <PERSON><PERSON><PERSON><PERSON> } = require("../mongodb/model");
const { Host } = require("../mongodb/model");
const { getExchangeRates } = require("./exchangeRateService");
const {
  isWithinRadius,
  isWithinBounds,
  isValidCoordinates,
  isValidBounds,
  milesToKm,
} = require("../utils/geoUtils");

/**
 * Create a new Dish
 * @param {Object} dishData - Data for creating a new dish
 * @returns {Object} Created dish
 */
const createDish = async (dishData) => {
  const dish = new Dish(dishData);
  return dish
    .save()
    .then((dish) => {
      console.log("Dish created successfully");
      return dish;
    })
    .catch((err) => {
      throw err;
    });
};

/**
 *
 * getHostByDishId
 * @param {String} dishId - Dish ID
 * @returns {Object|null} Host object or null if not found
 */
const getHostByDishId = async (dishId) => {
  const dish = await Dish.findById(dishId);
  if (!dish) {
    return null;
  }
  const host = await Host.findOne({ dishes: dishId });
  return host;
};

/**
 * Apply location-based filtering to dishes
 * @param {Array} dishes - Array of dishes with host information
 * @param {Object} locationFilters - Location filters object
 * @returns {Array} Filtered array of dishes
 */
const applyLocationFiltering = (dishes, locationFilters) => {
  const {
    latitude,
    longitude,
    radius,
    radiusUnit = "km",
    bounds,
  } = locationFilters;

  return dishes.filter((dish) => {
    // Skip dishes without host or host address
    if (!dish.host || !dish.host.address) {
      return false;
    }

    const hostAddress = dish.host.address;
    const hostLat = hostAddress.latitude;
    const hostLon = hostAddress.longitude;

    // Skip if host address doesn't have valid coordinates
    if (!isValidCoordinates(hostLat, hostLon)) {
      return false;
    }

    // Apply radius-based filtering
    if (
      latitude !== undefined &&
      longitude !== undefined &&
      radius !== undefined
    ) {
      if (!isValidCoordinates(latitude, longitude)) {
        return false;
      }

      const radiusInKm = radiusUnit === "miles" ? milesToKm(radius) : radius;
      return isWithinRadius(latitude, longitude, hostLat, hostLon, radiusInKm);
    }

    // Apply bounds-based filtering
    if (bounds && isValidBounds(bounds)) {
      return isWithinBounds(hostLat, hostLon, bounds);
    }

    // If no valid location filters are provided, include the dish
    return true;
  });
};

/**
 * Get all dishes with currency adjustment and advanced filtering including location-based filtering
 * @param {Object} filters - Filters for the query
 * @param {String} currency - Target currency for price conversion
 * @param {Object} locationFilters - Location-based filters
 * @param {number} locationFilters.latitude - Center latitude for radius search
 * @param {number} locationFilters.longitude - Center longitude for radius search
 * @param {number} locationFilters.radius - Search radius in kilometers (or miles if radiusUnit is 'miles')
 * @param {string} locationFilters.radiusUnit - Unit for radius ('km' or 'miles'), defaults to 'km'
 * @param {Object} locationFilters.bounds - Map bounds for rectangular area search
 * @param {number} locationFilters.bounds.north - Northern boundary
 * @param {number} locationFilters.bounds.south - Southern boundary
 * @param {number} locationFilters.bounds.east - Eastern boundary
 * @param {number} locationFilters.bounds.west - Western boundary
 * @returns {Array} Array of all dishes with adjusted prices
 */
const getAllDishes = async (
  filters = {},
  currency = "USD",
  locationFilters = {}
) => {
  // Removed debug logs for production
  const query = {};

  // Handle cuisine filter first at database level for optimal performance
  if (filters.cuisineQuery) {
    // Find cuisine IDs that match the query (case-insensitive)
    const matchingCuisines = await Cuisine.find({
      name: { $regex: filters.cuisineQuery, $options: "i" },
    }).select("_id name");

    if (matchingCuisines.length === 0) {
      return []; // No matching cuisines found
    }

    // Add cuisine filter to main query using ObjectIds
    const cuisineIds = matchingCuisines.map((c) => c._id);
    query.cuisine = { $in: cuisineIds };
  }

  // Basic filters
  if (typeof filters.published === "boolean") {
    query.published = filters.published;
  }
  if (typeof filters.premade === "boolean") {
    query.premade = filters.premade;
  }

  // Search filters
  if (filters.name) {
    query.name = filters.name;
  }
  if (filters.offering) {
    const offeringArray = filters.offering
      .split(",")
      .map((item) => item.trim());

    // For single value, just use the string directly
    if (offeringArray.length === 1) {
      query.offering = offeringArray[0];
    } else {
      // For multiple values, use $in
      query.offering = { $in: offeringArray };
    }
  }

  // Execute the optimized query with proper population
  let dishes = await Dish.find(query)
    .populate("cuisine")
    .populate("diningLocation");

  // Add host information to each dish
  const dishesWithHosts = await Promise.all(
    dishes.map(async (dish) => {
      const host = await Host.findOne({ dishes: dish._id })
        .select("_id title address")
        .populate("address");

      return {
        ...dish.toObject(),
        host: host ? host.toObject() : null,
      };
    })
  );

  // Apply location-based filtering if location filters are provided
  let filteredDishes = dishesWithHosts;

  if (locationFilters && Object.keys(locationFilters).length > 0) {
    filteredDishes = applyLocationFiltering(dishesWithHosts, locationFilters);
  }

  // Handle currency conversion if needed
  if (currency && currency !== "USD") {
    const rates = await getExchangeRates();
    return filteredDishes.map((dish) => {
      const rate = rates[currency] / (rates[dish.currency || "USD"] || 1);
      return {
        ...dish,
        price: (dish.price * rate).toFixed(2),
        currency,
      };
    });
  }

  return filteredDishes;
};

/**
 * Get a dish by ID with currency adjustment
 * @param {String} id - Dish ID
 * @param {String} currency - Target currency for price conversion
 * @returns {Object|null} Dish object with adjusted price or null if not found
 */
const getDishById = async (id, currency = "USD") => {
  const dish = await Dish.findById(id).populate("cuisine diningLocation");

  if (!dish) return null;

  if (currency && currency !== "USD") {
    const rates = await getExchangeRates();
    const rate = rates[currency] / (rates[dish.currency || "USD"] || 1);
    dish.price = (dish.price * rate).toFixed(2);
    dish.currency = currency;
  }
  const host = await Host.findOne({ dishes: id });
  dish.host = host;

  return dish;
};

/**
 * Update a dish by ID
 * @param {String} id - Dish ID
 * @param {Object} updateData - Data to update the dish
 * @returns {Object|null} Updated dish object or null if not found
 */
const updateDish = async (id, updateData) => {
  return await Dish.findByIdAndUpdate(id, updateData, {
    new: true,
    runValidators: true,
  });
};

/**
 * Delete a dish by ID
 * @param {String} id - Dish ID
 * @returns {Object|null} Deleted dish object or null if not found
 */
const deleteDish = async (id) => {
  return await Dish.findByIdAndDelete(id);
};

/**
 * getDishesByCuisine
 * @param {String} cuisine - Cuisine of the dish
 * @returns {Array} Array of dishes by cuisine
 */
const getDishesByCuisine = async (cuisine) => {
  return await Dish.find({ cuisine });
};

module.exports = {
  createDish,
  getAllDishes,
  getDishById,
  updateDish,
  deleteDish,
  getHostByDishId,
  getDishesByCuisine,
};
