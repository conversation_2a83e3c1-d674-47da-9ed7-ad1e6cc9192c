// src/services/requestService.js
const { Request, Response } = require("../mongodb/model"); // Assuming Response model is also exported from here
const { AppError } = require("../utils/error");
// const ResponseService = require("./responseService"); // May be needed if calling its methods directly

class RequestService {
  async createRequest(requestData) {
    const request = new Request(requestData);
    await request.save();
    return request.populate("user location");
  }

  async getActiveFeed({ location, page, limit }) {
    // Added city parameter
    const query = {
      status: "pending_responses", // Updated status
    };
    if (location) {
      query.city = location;
    }

    const skip = (page - 1) * limit;

    console.log("query ============>", query);

    const [requests, total] = await Promise.all([
      Request.find(query)
        .populate("user", "firstName lastName profilePicture averageRating") // Adjusted fields
        .populate("location")
        .sort("-createdAt")
        .skip(skip)
        .limit(limit),
      Request.countDocuments(query),
    ]);

    return {
      requests,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async getRequestById(requestId) {
    const request = await Request.findById(requestId)
      .populate("user", "firstName lastName profilePicture averageRating") // Adjusted fields
      .populate("location")
      .populate({
        // Populate the accepted response and its details
        path: "acceptedResponseId",
        populate: [
          {
            path: "host",
            select: "firstName lastName profilePicture averageRating",
          },
          { path: "dish" },
        ],
      });

    if (!request) {
      throw new AppError("Request not found", 404);
    }
    return request;
  }

  async acceptResponse(requestId, responseId, userId) {
    const request = await Request.findOne({
      _id: requestId,
      user: userId, // Ensure the user owns the request
    });

    if (!request) {
      throw new AppError("Request not found or you are not authorized", 404);
    }

    // Check if the request is in a state where a response can be accepted
    if (
      request.status !== "pending_responses" &&
      request.status !== "awaiting_acceptance"
    ) {
      throw new AppError(
        `Request cannot be updated in its current status: ${request.status}`,
        400
      );
    }

    // Verify the response exists and belongs to this request (optional, but good practice)
    const responseToAccept = await Response.findOne({
      _id: responseId,
      request: requestId,
    });
    if (!responseToAccept) {
      throw new AppError(
        "Response not found or does not belong to this request",
        404
      );
    }

    // Update the Request
    request.status = "confirmed";
    request.acceptedResponseId = responseId;
    await request.save();

    // Update the Response status (in Response collection)
    // This might be better handled by calling a ResponseService method
    // For now, directly updating here for simplicity, assuming Response model is available
    responseToAccept.status = "accepted_by_user";
    await responseToAccept.save();

    // TODO: Notify other hosts whose responses were not chosen that the request is now closed.

    return request.populate({
      // Populate the accepted response and its details
      path: "acceptedResponseId",
      populate: [
        {
          path: "host",
          select: "firstName lastName profilePicture averageRating",
        },
        { path: "dish" },
      ],
    });
  }

  async getUserRequests(userId, { status, page, limit }) {
    const query = { user: userId };

    if (status) {
      query.status = status;
    }

    const skip = (page - 1) * limit;

    const [requests, total] = await Promise.all([
      Request.find(query)
        .populate("location")
        .populate({
          // Populate the accepted response and its details
          path: "acceptedResponseId",
          populate: [
            {
              path: "host",
              select: "firstName lastName profilePicture averageRating",
            },
            { path: "dish" },
          ],
        })
        .sort("-createdAt")
        .skip(skip)
        .limit(limit),
      Request.countDocuments(query),
    ]);

    return {
      requests,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit),
      },
    };
  }
  // getHostResponses method is removed as it's now in ResponseService
}

module.exports = RequestService; // Export class
