# Frontend Integration Guide: Request System

## I. Introduction

This document provides frontend developers with the necessary information to integrate with the backend 'request system'. The system allows users to request specific dishes, hosts to respond with offers, and users to accept these offers, facilitating a dynamic food ordering process.

The primary architectural approach involves using:

- **RESTful APIs** for performing core actions (creating requests, responding, accepting).
- **WebSockets** for receiving real-time updates and notifications related to the request lifecycle.

## II. Authentication and Authorization

Secure communication is ensured through JWT (JSON Web Tokens) for both API calls and WebSocket connections.

### A. API Authentication

1.  **Method**: JWT Bearer Token.
2.  **Header**: All protected API endpoints require an `Authorization` header.
    ```
    Authorization: Bearer <YOUR_JWT_TOKEN>
    ```
3.  **Obtaining the Token**: The JWT token is obtained upon successful user login/authentication. Refer to the main authentication system documentation for details on login endpoints.
4.  **Token Expiration**: Tokens have an expiration time (e.g., 1 hour for test tokens). Frontend clients should be prepared to handle `401 Unauthorized` responses by prompting re-authentication or using a token refresh mechanism if available.

### B. WebSocket Authentication

1.  **Method**: A valid JWT Token must be provided when establishing the WebSocket connection.
2.  **Token Sources**: The server will attempt to extract the token from the following sources in order:
    - `Authorization` header in the WebSocket handshake (e.g., `socket.handshake.headers.authorization: 'Bearer <YOUR_JWT_TOKEN>'`).
    - `auth.token` property in the WebSocket handshake options (e.g., `socket = io({ auth: { token: '<YOUR_JWT_TOKEN>' } })`).
    - `token` query parameter in the WebSocket connection URI (e.g., `?token=<YOUR_JWT_TOKEN>`).
3.  **Error Handling**: If authentication fails, the WebSocket connection will likely be rejected, or an error event will be emitted by the server. Common error codes sent by the server upon connection or authentication failure include:
    - `AUTH_TOKEN_REQUIRED`: No token was provided.
    - `AUTH_TOKEN_EXPIRED`: The provided token has expired.
    - `AUTH_TOKEN_INVALID`: The token is malformed or invalid.
    - `AUTH_FAILED_INVALID_TOKEN`: General invalid token error.
    - `AUTH_MIDDLEWARE_ERROR`: A server-side error occurred during authentication.

### C. User Roles

The system distinguishes between user types, typically 'user' (who creates requests) and 'host' (who responds to requests). This is often included in the JWT payload (e.g., `decoded.type`). API endpoints and WebSocket event handling might differ based on the authenticated user's role.

## III. API Endpoints (Request System)

All API endpoints are relative to the base API URL (e.g., `/api`).

---

### A. Create a New Dish Request

- **HTTP Method**: `POST`
- **URL Path**: `/requests`
- **Required Headers**:
  - `Authorization: Bearer <YOUR_JWT_TOKEN>`
  - `Content-Type: application/json`
- **Request Body Schema**:
  ```json
  {
    "dishRequest": {
      "title": "string (required)",
      "ingredients": ["string", "(required, at least one)"],
      "description": "string (optional, default: '')",
      "offering": "string (enum: 'Dine In', 'Take Away', 'Delivery', optional)",
      "minServing": "number (optional, default: 1, min: 1)",
      "maxServing": "number (optional, default: 1, min: 1)",
      "quantity": "number (optional, default: 1, min: 1)",
      "offeringTime": "ISO8601 Date string (optional)"
    },
    "location": {
      "street": "string (required)",
      "city": "string (required)",
      "state": "string (required)",
      "postalCode": "string (required, regex: /^[A-Z0-9]{1,10}([\\s-][A-Z0-9]{1,10})?$/i)",
      "country": "string (required)",
      "latitude": "number (required, min: -90, max: 90)",
      "longitude": "number (required, min: -180, max: 180)",
      "formattedAddress": "string (optional)",
      "placeId": "string (optional)"
    }
  }
  ```
- **Response Body Schema (201 Created)**: The created Request object.
  ```json
  {
    "_id": "ObjectId",
    "user": "ObjectId (ref: users)",
    "dishRequest": {
      /* as above */
    },
    "status": "string (default: 'pending_responses')",
    "location": "ObjectId (ref: Address)",
    "acceptedResponseId": null,
    "responseCount": 0,
    "expiresAt": "ISO8601 Date string (optional)",
    "createdAt": "ISO8601 Date string",
    "updatedAt": "ISO8601 Date string"
  }
  ```
- **Status Codes**:
  - `201 Created`: Request successfully created.
  - `400 Bad Request`: Invalid input data or schema validation failed.
  - `401 Unauthorized`: Authentication token missing or invalid.
  - `500 Internal Server Error`: Server-side error.

---

### B. Get Request Feed (for Hosts)

- **HTTP Method**: `GET`
- **URL Path**: `/requests/feed`
- **Required Headers**:
  - `Authorization: Bearer <YOUR_JWT_TOKEN>`
- **Query Parameters**:
  - `location`: `string` (e.g., city name to filter requests, optional)
  - `page`: `number` (optional, default: 1)
  - `limit`: `number` (optional, default: 20)
- **Response Body Schema (200 OK)**: Paginated list of active Request objects.
  ```json
  {
    "data": [
      /* Array of Request objects */
    ],
    "page": "number",
    "limit": "number",
    "totalPages": "number",
    "totalResults": "number"
  }
  ```
- **Status Codes**:
  - `200 OK`: Successfully retrieved request feed.
  - `401 Unauthorized`: Authentication token missing or invalid.
  - `500 Internal Server Error`: Server-side error.

---

### C. Get User's Own Requests

- **HTTP Method**: `GET`
- **URL Path**: `/requests/user`
- **Required Headers**:
  - `Authorization: Bearer <YOUR_JWT_TOKEN>`
- **Query Parameters**:
  - `status`: `string` (optional, e.g., "pending_responses", "confirmed")
  - `page`: `number` (optional, default: 1)
  - `limit`: `number` (optional, default: 20)
- **Response Body Schema (200 OK)**: Paginated list of Request objects belonging to the authenticated user. (Same structure as `/requests/feed` response).
- **Status Codes**:
  - `200 OK`: Successfully retrieved user's requests.
  - `401 Unauthorized`: Authentication token missing or invalid.
  - `500 Internal Server Error`: Server-side error.

---

### D. Get Specific Request by ID

- **HTTP Method**: `GET`
- **URL Path**: `/requests/:requestId`
- **Required Headers**:
  - `Authorization: Bearer <YOUR_JWT_TOKEN>`
- **Response Body Schema (200 OK)**: A single Request object.
- **Status Codes**:
  - `200 OK`: Successfully retrieved the request.
  - `401 Unauthorized`: Authentication token missing or invalid.
  - `404 Not Found`: Request with the given ID not found.
  - `500 Internal Server Error`: Server-side error.

---

### E. Get Host's Responses to Various Requests

- **HTTP Method**: `GET`
- **URL Path**: `/requests/host-responses`
- **Required Headers**:
  - `Authorization: Bearer <YOUR_JWT_TOKEN>` (Host's token)
- **Query Parameters**:
  - `status`: `string` (optional, e.g., "pending_user_action", "accepted_by_user")
  - `page`: `number` (optional, default: 1)
  - `limit`: `number` (optional, default: 20)
- **Response Body Schema (200 OK)**: Paginated list of Response objects made by the authenticated host.
  ```json
  {
    "data": [
      {
        "_id": "ObjectId",
        "request": "ObjectId (ref: Request)",
        "host": "ObjectId (ref: users)",
        "dish": "ObjectId (ref: Dish)",
        "message": "string (optional)",
        "status": "string (default: 'pending_user_action')",
        "createdAt": "ISO8601 Date string",
        "updatedAt": "ISO8601 Date string"
      }
      /* ... more response objects */
    ],
    "page": "number",
    "limit": "number",
    "totalPages": "number",
    "totalResults": "number"
  }
  ```
- **Status Codes**:
  - `200 OK`: Successfully retrieved host's responses.
  - `401 Unauthorized`: Authentication token missing or invalid.
  - `500 Internal Server Error`: Server-side error.

---

### F. Get All Responses for a Specific Request

- **HTTP Method**: `GET`
- **URL Path**: `/requests/:requestId/responses`
- **Required Headers**:
  - `Authorization: Bearer <YOUR_JWT_TOKEN>`
- **Query Parameters**:
  - `page`: `number` (optional, default: 1)
  - `limit`: `number` (optional, default: 20)
- **Response Body Schema (200 OK)**: Paginated list of Response objects for the given `requestId`. (Same structure as `/requests/host-responses` response).
- **Status Codes**:
  - `200 OK`: Successfully retrieved responses for the request.
  - `401 Unauthorized`: Authentication token missing or invalid.
  - `404 Not Found`: Request with the given ID not found.
  - `500 Internal Server Error`: Server-side error.

---

### G. Respond to a Request (by Host)

- **HTTP Method**: `POST`
- **URL Path**: `/requests/:requestId/responses`
- **Required Headers**:
  - `Authorization: Bearer <YOUR_JWT_TOKEN>` (Host's token)
  - `Content-Type: application/json`
- **Request Body Schema**:
  ```json
  {
    "message": "string (optional)",
    // Dish Data (fields from Dish schema, e.g., name, price, description, ingredients, cuisine)
    // Note: isResponseSpecific will be set to true, originalRequestId to :requestId by backend.
    "name": "string (required)",
    "price": "number (required)",
    "description": "string (required)",
    "photos": ["string (URL, required, at least one)"],
    "ingredients": ["string (required, at least one)"],
    "cuisine": "ObjectId (ref: Cuisine, required)",
    "minServings": "number (required)",
    "maxServings": "number (required)",
    "availability": [
      {
        "date": "ISO8601 Date string (required)",
        "startTime": "string (HH:MM, required)",
        "endTime": "string (HH:MM, required)"
      }
    ]
    // ... other relevant Dish fields
  }
  ```
- **Response Body Schema (201 Created)**: The created Response object.
- **Status Codes**:
  - `201 Created`: Response successfully submitted.
  - `400 Bad Request`: Invalid input data or request is not active for responses.
  - `401 Unauthorized`: Authentication token missing or invalid.
  - `404 Not Found`: Request with the given ID not found.
  - `500 Internal Server Error`: Server-side error.

---

### H. Accept a Response (by User who created the Request)

- **HTTP Method**: `POST`
- **URL Path**: `/requests/:requestId/responses/:responseId/accept`
- **Required Headers**:
  - `Authorization: Bearer <YOUR_JWT_TOKEN>` (User's token)
- **Request Body Schema**: (Empty or not typically required)
  ```json
  {}
  ```
- **Response Body Schema (200 OK)**: The updated Request object (with `acceptedResponseId` populated and `status` changed, e.g., to "confirmed").
- **Status Codes**:
  - `200 OK`: Response successfully accepted.
  - `400 Bad Request`: Invalid request or response ID, or response cannot be accepted.
  - `401 Unauthorized`: Authentication token missing or invalid.
  - `403 Forbidden`: Authenticated user is not the creator of the request.
  - `404 Not Found`: Request or Response with the given ID not found.
  - `500 Internal Server Error`: Server-side error.

## IV. WebSocket Communication (Request System)

WebSockets provide real-time updates for events within the request system.

### A. Connection

1.  **URI**: `your_base_server_url/socket.io` (Replace `your_base_server_url` with the actual server URL. Use `ws://` or `wss://` as appropriate).
2.  **Authentication**: As detailed in section II.B.
3.  **Key Server-Sent Connection Events**:
    - **`connection:success`**: Emitted by the server upon successful connection and authentication.
      - Payload:
        ```json
        {
          "message": "Successfully connected to WebSocket server",
          "userId": "string (authenticated user's ID)",
          "userType": "string ('user' or 'host')",
          "socketId": "string (current socket ID)"
        }
        ```
    - **`error`**: Emitted by the server if an error occurs (e.g., authentication failure, server error during connection handling).
      - Payload:
        ```json
        {
          "code": "string (e.g., 'AUTH_TOKEN_REQUIRED', 'CONNECTION_SETUP_FAILED')",
          "message": "string (description of the error)",
          "details": "string (optional, further error details)"
        }
        ```

### B. Client-Sent Events (for Hosts)

1.  **`host:register`**
    - **Purpose**: For a host client to subscribe to new request feeds based on their operational location. This allows them to receive `feed:newRequest` events relevant to them.
    - **Payload**:
      ```json
      {
        "location": "string (e.g., city name or specific area identifier)"
      }
      ```
    - **Server Response/Confirmation**: The server may emit a `host:registered` event back to the client.
      - Event Name: `host:registered`
      - Payload:
        ```json
        {
          "success": true,
          "location": "string (the location registered)"
        }
        ```

### C. Server-Sent Events (Real-time Updates for Request System)

Clients should listen for these events to update their UI in real-time.

1.  **`feed:newRequest`** (Emitted to relevant hosts)

    - **Payload**:
      ```json
      {
        "request": {
          /* New Request Object schema */
        },
        "location": "string (city of the request)"
      }
      ```
    - **Trigger**: After a new dish request is successfully created by a user via `POST /requests`.
    - **Audience**: Hosts who have registered for the location matching the request's location.

2.  **`request:newResponse`** (Emitted to the user who created the request)

    - **Payload**:
      ```json
      {
        "requestId": "string (ID of the original request)",
        "response": {
          /* New Response Object schema */
        }
      }
      ```
    - **Trigger**: After a host successfully submits a response to a request via `POST /requests/:requestId/responses`.

3.  **`response:accepted`** (Emitted to the host whose response was accepted)

    - **Payload**:
      ```json
      {
        "requestId": "string (ID of the request)",
        "responseId": "string (ID of the accepted response)",
        "requestTitle": "string (title of the original dish request)"
      }
      ```
    - **Trigger**: After a user successfully accepts a response via `POST /requests/:requestId/responses/:responseId/accept`.

4.  **`request:updated`** (Emitted to the user who created the request, after they accept a response)

    - **Payload**:
      ```json
      {
        "request": {
          /* Updated Request Object schema, status likely 'confirmed' */
        }
      }
      ```
    - **Trigger**: After a user successfully accepts a response, providing the full updated state of their request.

5.  **`feed:requestInactive`** (Emitted to relevant hosts)
    - **Payload**:
      ```json
      {
        "requestId": "string (ID of the request that is no longer active)"
      }
      ```
    - **Trigger**: After a user successfully accepts a response for a request (via `POST /requests/:requestId/responses/:responseId/accept`), indicating this request should no longer be considered active in feeds.
    - **Audience**: Hosts who might have originally seen this request in their feed (e.g., hosts in the same location as the original request).

## V. Code Snippets (JavaScript Examples)

These examples use `fetch` for API calls and the `socket.io-client` library for WebSocket interactions.

### A. API Calls

**1. Creating a New Dish Request**

```javascript
async function createDishRequest(requestData, token) {
  try {
    const response = await fetch("/api/requests", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(requestData),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || `HTTP error! status: ${response.status}`
      );
    }
    return await response.json();
  } catch (error) {
    console.error("Error creating dish request:", error);
    throw error;
  }
}

// Example usage:
// const newRequestPayload = { /* ... your request data ... */ };
// const userToken = 'your_jwt_token_here';
// createDishRequest(newRequestPayload, userToken)
//   .then(createdRequest => console.log('Request created:', createdRequest))
//   .catch(err => console.error(err));
```

**2. Responding to a Request (Host)**

```javascript
async function respondToRequest(requestId, responseData, hostToken) {
  try {
    const response = await fetch(`/api/requests/${requestId}/responses`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${hostToken}`,
      },
      body: JSON.stringify(responseData),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || `HTTP error! status: ${response.status}`
      );
    }
    return await response.json();
  } catch (error) {
    console.error("Error responding to request:", error);
    throw error;
  }
}
```

**3. Accepting a Response (User)**

```javascript
async function acceptResponse(requestId, responseId, userToken) {
  try {
    const response = await fetch(
      `/api/requests/${requestId}/responses/${responseId}/accept`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${userToken}`,
        },
        // No body typically needed for this action
      }
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || `HTTP error! status: ${response.status}`
      );
    }
    return await response.json(); // Updated request object
  } catch (error) {
    console.error("Error accepting response:", error);
    throw error;
  }
}
```

### B. WebSocket Interactions

```javascript
import { io } from "socket.io-client";

const SOCKET_URL = "your_base_server_url"; // e.g., 'ws://localhost:3001' or 'wss://api.example.com'
const userToken = "your_jwt_token_here";

// 1. Connecting to the WebSocket server with authentication
const socket = io(SOCKET_URL, {
  path: "/socket.io", // Ensure this matches server config
  auth: {
    token: userToken,
  },
  // Alternatively, using headers:
  // extraHeaders: {
  //   Authorization: `Bearer ${userToken}`
  // }
});

socket.on("connect", () => {
  console.log("Successfully connected to WebSocket server with ID:", socket.id);
});

socket.on("connection:success", (data) => {
  console.log("Server confirmed connection:", data);
  // If user is a host, they might register their location
  if (data.userType === "host") {
    // 2. Example: Host client sending host:register
    socket.emit("host:register", { location: "Downtown" });
  }
});

socket.on("host:registered", (data) => {
  console.log("Host registration confirmed by server:", data);
});

// 3. Example: Client listening for feed:newRequest (typically for hosts)
socket.on("feed:newRequest", (data) => {
  console.log("New dish request received in feed:", data.request);
  // Update host's UI with the new request
});

// 4. Example: Client listening for request:newResponse (typically for users who made the request)
socket.on("request:newResponse", (data) => {
  console.log(
    `New response for your request ${data.requestId}:`,
    data.response
  );
  // Update user's UI to show the new response
});

// 5. Example: Client listening for response:accepted (typically for hosts whose response was chosen)
socket.on("response:accepted", (data) => {
  console.log(
    `Your response ${data.responseId} for request ${data.requestId} was accepted!`
  );
  // Update host's UI accordingly
});

socket.on("request:updated", (data) => {
  console.log("Your request has been updated:", data.request);
  // Update user's UI with the latest request status (e.g., after accepting a response)
});

socket.on("feed:requestInactive", (data) => {
  console.log(`Request ${data.requestId} is now inactive in the feed.`);
  // Remove request from host's active feed UI
});

socket.on("disconnect", (reason) => {
  console.log("Disconnected from WebSocket server:", reason);
});

socket.on("connect_error", (error) => {
  console.error("WebSocket connection error:", error.message, error.data);
  // Handle specific auth errors if error.data contains codes like AUTH_TOKEN_EXPIRED
  if (error.data && error.data.code === "AUTH_TOKEN_EXPIRED") {
    // Handle token expiration, e.g., prompt for re-login
  }
});

socket.on("error", (errorData) => {
  // Generic error event from server
  console.error("Server emitted an error:", errorData);
});

// To send a message (if using common 'message' event for generic comms)
// socket.emit('message', { text: 'Hello from client!' });
// socket.on('message:received', (data) => {
//   console.log('Message received from server:', data);
// });
```

## VI. Workflow Diagrams (Mermaid)

### A. User Request Creation and Host Notification Flow

```mermaid
sequenceDiagram
    participant FE_User as Frontend (User)
    participant API as Backend API
    participant WS_Server as WebSocket Server
    participant FE_Host as Frontend (Host)

    FE_User->>+API: POST /requests (Create Request)
    API-->>-FE_User: 201 Created (Request Object)
    API->>WS_Server: New Request Data (for broadcast)
    WS_Server-->>FE_Host: feed:newRequest (payload)
```

### B. Host Response and User Notification Flow

```mermaid
sequenceDiagram
    participant FE_Host as Frontend (Host)
    participant API as Backend API
    participant WS_Server as WebSocket Server
    participant FE_User as Frontend (User)

    FE_Host->>+API: POST /requests/{reqId}/responses (Submit Response)
    API-->>-FE_Host: 201 Created (Response Object)
    API->>WS_Server: New Response Data (for user)
    WS_Server-->>FE_User: request:newResponse (payload)
```

### C. User Accepts Response and Host Notification Flow

```mermaid
sequenceDiagram
    participant FE_User as Frontend (User)
    participant API as Backend API
    participant WS_Server as WebSocket Server
    participant FE_Chosen_Host as Frontend (Chosen Host)
    participant FE_Other_Hosts as Frontend (Other Relevant Hosts)

    FE_User->>+API: POST /requests/{reqId}/responses/{respId}/accept (Accept Response)
    API-->>-FE_User: 200 OK (Updated Request Object)
    API->>WS_Server: Accepted Response Data (for chosen host)
    WS_Server-->>FE_Chosen_Host: response:accepted (payload)
    API->>WS_Server: Updated Request Data (for user self-update)
    WS_Server-->>FE_User: request:updated (payload)
    API->>WS_Server: Request Inactive Data (for other relevant hosts, using request's location)
    WS_Server-->>FE_Other_Hosts: feed:requestInactive (payload)
```

## VII. Appendix (Optional)

### A. Mongoose Schemas (Summary for Reference)

**DishRequestSchema (Embedded in Request)**

- `title`: String, required
- `ingredients`: Array of String, required
- `description`: String
- `offering`: String (enum: "Dine In", "Take Away", "Delivery")
- `minServing`, `maxServing`, `quantity`: Number
- `offeringTime`: Date

**RequestSchema**

- `user`: ObjectId (ref: 'users'), required
- `dishRequest`: DishRequestSchema
- `status`: String (enum: "pending_responses", "awaiting_acceptance", "confirmed", "completed", "cancelled", "expired"), default: "pending_responses"
- `location`: ObjectId (ref: 'Address')
- `acceptedResponseId`: ObjectId (ref: 'Response'), default: null
- `responseCount`: Number, default: 0
- `expiresAt`: Date

**ResponseSchema**

- `request`: ObjectId (ref: 'Request'), required
- `host`: ObjectId (ref: 'users'), required
- `dish`: ObjectId (ref: 'Dish'), required
- `message`: String
- `status`: String (enum: "pending_user_action", "accepted_by_user", "rejected_by_user", "withdrawn_by_host"), default: "pending_user_action"

**DishSchema (Relevant fields for response-specific dish)**

- `name`, `price`, `description`: String/Number, required
- `photos`, `ingredients`: Array of String, required
- `cuisine`: ObjectId (ref: 'Cuisine'), required
- `minServings`, `maxServings`: Number, required
- `availability`: Array of { date, startTime, endTime }
- `isResponseSpecific`: Boolean (true for these dishes)
- `originalRequestId`: ObjectId (ref: 'Request')

**AddressSchema**

- `street`, `city`, `state`, `postalCode`, `country`: String, required
- `latitude`, `longitude`: Number, required
- `formattedAddress`, `placeId`: String

### B. Common Error Codes and Meanings

- **API Errors (HTTP Status Codes)**:
  - `400 Bad Request`: Client-side input error (e.g., missing fields, invalid format).
  - `401 Unauthorized`: Missing, invalid, or expired authentication token.
  - `403 Forbidden`: Authenticated user does not have permission for the action.
  - `404 Not Found`: Requested resource does not exist.
  - `500 Internal Server Error`: Unexpected error on the server.
- **WebSocket Connection Errors (emitted by client or server `error` event)**:
  - `AUTH_TOKEN_REQUIRED`: Authentication token not provided during handshake.
  - `AUTH_TOKEN_EXPIRED`: Provided token is expired.
  - `AUTH_TOKEN_INVALID`: Token is malformed or signature is invalid.
  - `CONNECTION_SETUP_FAILED`: Server error during the socket connection setup phase.

---

**Note**: This documentation is based on the analysis of the existing codebase. Ensure to test integrations thoroughly. Field names, types, and behaviors should be cross-verified with the latest backend implementation.
