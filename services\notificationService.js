const { Notification } = require("../mongodb/model");
const socketManager = require("../utils/websocket");

// Track recent WebSocket notifications to prevent duplicates
const recentWebSocketNotifications = new Map();
const WEBSOCKET_DUPLICATE_WINDOW = 30000; // 30 seconds

/**
 * Service for handling notifications
 */
class NotificationService {
  /**
   * Create a new notification
   * @param {Object} notificationData - Notification data
   * @returns {Promise<Object>} Created notification
   */
  async createNotification(notificationData) {
    try {
      const notification = new Notification(notificationData);
      await notification.save();

      // Send real-time notification via socket if recipient is connected
      this.sendRealTimeNotification(notification);

      return notification;
    } catch (error) {
      console.error("Error creating notification:", error);
      throw error;
    }
  }

  /**
   * Get notifications for a user
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} List of notifications
   */
  async getUserNotifications(userId, options = {}) {
    try {
      const { limit = 20, skip = 0, isRead } = options;

      const query = { recipient: userId };

      if (isRead !== undefined) {
        query.isRead = isRead;
      }

      const notifications = await Notification.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate("relatedOrder", "orderNumber")
        .populate("relatedHost", "title");

      return notifications;
    } catch (error) {
      console.error("Error fetching user notifications:", error);
      throw error;
    }
  }

  /**
   * Mark notification as read
   * @param {string} notificationId - Notification ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Updated notification
   */
  async markAsRead(notificationId, userId) {
    try {
      const notification = await Notification.findOneAndUpdate(
        { _id: notificationId, recipient: userId },
        { isRead: true },
        { new: true }
      );

      return notification;
    } catch (error) {
      console.error("Error marking notification as read:", error);
      throw error;
    }
  }

  /**
   * Mark all notifications as read for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Result of the operation
   */
  async markAllAsRead(userId) {
    try {
      const result = await Notification.updateMany(
        { recipient: userId, isRead: false },
        { isRead: true }
      );

      return result;
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      throw error;
    }
  }

  /**
   * Get unread notification count for a user
   * @param {string} userId - User ID
   * @returns {Promise<number>} Count of unread notifications
   */
  async getUnreadCount(userId) {
    try {
      const count = await Notification.countDocuments({
        recipient: userId,
        isRead: false,
      });

      return count;
    } catch (error) {
      console.error("Error counting unread notifications:", error);
      throw error;
    }
  }

  /**
   * Create order notification
   * @param {string} userId - User ID
   * @param {string} orderId - Order ID
   * @param {string} orderNumber - Order number
   * @param {string} type - Notification type
   * @param {string} status - Order status
   */
  async createOrderNotification(
    userId,
    orderId,
    orderNumber,
    type,
    status = null
  ) {
    try {
      let title, message;

      switch (type) {
        case "order_created":
          title = "Order Created";
          message = `Your order #${orderNumber} has been created successfully.`;
          break;
        case "order_status_changed":
          title = "Order Status Updated";
          message = `Your order #${orderNumber} status has been updated to ${status}.`;
          break;
        case "order_payment_success":
          title = "Payment Successful";
          message = `Payment for your order #${orderNumber} was successful.`;
          break;
        case "order_payment_failed":
          title = "Payment Failed";
          message = `Payment for your order #${orderNumber} has failed. Please try again.`;
          break;
        default:
          title = "Order Update";
          message = `There is an update for your order #${orderNumber}.`;
      }

      const notificationData = {
        recipient: userId,
        type,
        title,
        message,
        relatedOrder: orderId,
        data: {
          orderId,
          orderNumber,
          status,
        },
      };

      return await this.createNotification(notificationData);
    } catch (error) {
      console.error("Error creating order notification:", error);
      throw error;
    }
  }

  /**
   * Create host order notification
   * @param {string} hostUserId - Host user ID
   * @param {string} hostId - Host ID
   * @param {string} orderId - Order ID
   * @param {string} orderNumber - Order number
   * @param {string} type - Notification type
   * @param {string} status - Order status
   */
  async createHostOrderNotification(
    hostUserId,
    hostId,
    orderId,
    orderNumber,
    type,
    status = null
  ) {
    try {
      console.log(
        `🔔 DEBUG: Creating host notification for host ${hostUserId}, order ${orderId}, type ${type}`
      );
      console.log(`🔔 DEBUG: Call stack for notification:`, new Error().stack);

      // Check for duplicate notifications
      const existingNotification = await Notification.findOne({
        recipient: hostUserId,
        relatedOrder: orderId,
        type: type,
        createdAt: { $gte: new Date(Date.now() - 30000) }, // Within last 30 seconds
      });

      if (existingNotification) {
        console.log(
          `⚠️  DEBUG: Duplicate notification detected for host ${hostUserId}, order ${orderId}, type ${type} - skipping`
        );
        return existingNotification;
      }

      console.log(
        "createHostOrderNotification",
        hostUserId,
        hostId,
        orderId,
        orderNumber,
        type,
        status
      );
      let title, message;

      switch (type) {
        case "host_order_received":
          title = "New Order Received";
          message = `You have received a new order #${orderNumber}.`;
          break;
        case "host_order_status_changed":
          title = "Order Status Updated";
          message = `Order #${orderNumber} status has been updated to ${status}.`;
          break;
        default:
          title = "Order Update";
          message = `There is an update for order #${orderNumber}.`;
      }

      const notificationData = {
        recipient: hostUserId,
        type,
        title,
        message,
        relatedOrder: orderId,
        relatedHost: hostId,
        data: {
          orderId,
          orderNumber,
          status,
        },
      };

      return await this.createNotification(notificationData);
    } catch (error) {
      console.error("Error creating host order notification:", error);
      throw error;
    }
  }

  /**
   * Send real-time notification via socket
   * @param {Object} notification - Notification object
   */
  sendRealTimeNotification(notification) {
    try {
      const userId = notification.recipient.toString();
      const notificationKey = `${userId}:${notification.type}:${notification.relatedOrder}`;

      console.log(
        `🔊 DEBUG: Sending WebSocket notification to user:${userId} for type ${notification.type}, order ${notification.relatedOrder}`
      );
      console.log(`🔊 DEBUG: WebSocket call stack:`, new Error().stack);

      // Check for recent duplicate WebSocket notifications
      const now = Date.now();
      const lastSent = recentWebSocketNotifications.get(notificationKey);

      if (lastSent && now - lastSent < WEBSOCKET_DUPLICATE_WINDOW) {
        console.log(
          `⚠️ DEBUG: Skipping duplicate WebSocket notification for ${notificationKey} (sent ${
            now - lastSent
          }ms ago)`
        );
        return;
      }

      // Track this notification
      recentWebSocketNotifications.set(notificationKey, now);

      // Clean up old entries (prevent memory leak)
      for (const [key, timestamp] of recentWebSocketNotifications.entries()) {
        if (now - timestamp > WEBSOCKET_DUPLICATE_WINDOW) {
          recentWebSocketNotifications.delete(key);
        }
      }

      // Emit to user's room
      socketManager.io
        .to(`user:${userId}`)
        .emit("notification:new", notification);

      console.log(`📢 Sent real-time notification to user:${userId}`);
    } catch (error) {
      console.error("Error sending real-time notification:", error);
    }
  }
}

module.exports = new NotificationService();
