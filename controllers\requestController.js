// src/controllers/requestController.js
const RequestService = require("../services/requestService");
const ResponseService = require("../services/responseService"); // New
const hostService = require("../services/hostService");
const { Dish, Request } = require("../mongodb/model"); // Assuming Dish and Request models are exported from here
const addressService = require("../services/addressService");
const { AppError } = require("../utils/error");
const { NOT_FOUND } = require("../constants/errorCodes");

class RequestController {
  constructor() {
    this.requestService = new RequestService();
    this.responseService = new ResponseService(); // New
  }

  createRequest = async (req, res, next) => {
    try {
      const { user } = req;
      const { location, ...dishRequestDetails } = req.body; // Separate location and city from other details

      const locationData = await addressService.createAddress(location);
      const requestData = {
        dishRequest: dishRequestDetails, // Assuming req.body now contains dishRequest fields directly
        location: locationData._id,
        city: locationData.city,
        user: user.id,
      };

      const newRequest = await this.requestService.createRequest(requestData);

      res.status(201).json(newRequest);
    } catch (error) {
      next(error);
    }
  };

  getRequestFeed = async (req, res, next) => {
    try {
      const { user } = req;
      console.log("user ============>", user);
      const host = await hostService.getHostByUserId(user.id);
      if (!host) {
        return res.status(404).json({ message: "Host not found" });
      }
      console.log("host ============>", host);
      const { page = 1, limit = 20 } = req.query; // Added city
      const feed = await this.requestService.getActiveFeed({
        location: host?.address?.city,
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
      });
      res.json(feed);
    } catch (error) {
      next(error);
    }
  };

  getRequestbyId = async (req, res, next) => {
    try {
      const { user } = req;
      const { requestId } = req.params;

      const request = await this.requestService.getRequestById(requestId);

      if (!request) {
        return res.status(404).json({ message: "Request not found" });
      }

      const responsePayload = await this.responseService.getResponsesForRequest(
        requestId
      );
      // Extract the actual responses array, defaulting to an empty array if payload or data is missing
      const responses =
        responsePayload && responsePayload.data ? responsePayload.data : [];

      const userResponse = responses.find((response) => {
        // Ensure robust checking for nested properties before calling toString()
        const hostId =
          response && response.host && response.host._id
            ? response.host._id.toString()
            : null;
        const currentUserId = user && user.id ? user.id.toString() : null;
        // Ensure both IDs are valid strings before comparing
        return hostId && currentUserId && hostId === currentUserId;
      });

      // Create a plain object copy to safely add userResponse property.
      // This is important if 'request' is a Mongoose document.
      let finalRequestToSend = request.toObject
        ? request.toObject()
        : JSON.parse(JSON.stringify(request));

      if (userResponse) {
        finalRequestToSend.userResponse = userResponse;
      }

      res.json(finalRequestToSend);
    } catch (error) {
      // For production, consider a more robust logging solution than console.error
      // console.error("Error in getRequestbyId:", error);
      next(error);
    }
  };

  respondToRequest = async (req, res, next) => {
    try {
      const { requestId } = req.params;
      const { user } = req; // This is the host
      const { message, dishId } = req.body; // Separate message from dish data

      // 1. Verify the original request exists and is active
      const originalRequest = await this.requestService.getRequestById(
        requestId
      );
      if (!originalRequest) {
        throw new AppError("Request not found", 404);
      }
      if (originalRequest.status !== "pending_responses") {
        throw new AppError(
          `Request is not active for responses (status: ${originalRequest.status})`,
          400
        );
      }

      // 3. Create the Response document
      const responsePayload = {
        request: requestId,
        host: user.id,
        dish: dishId,
        message,
      };
      const newResponse = await this.responseService.createResponse(
        responsePayload
      );

      // 4. Increment responseCount on the original Request (optional, but good for UI)
      originalRequest.responseCount = (originalRequest.responseCount || 0) + 1;
      // Potentially change status if it's the first response, e.g., to 'awaiting_acceptance'
      // For now, just increment count.
      await originalRequest.save();

      res.status(201).json(newResponse);
    } catch (error) {
      next(error);
    }
  };

  acceptResponse = async (req, res, next) => {
    try {
      const { requestId, responseId } = req.params;
      const { user } = req; // This is the user who created the request

      const updatedRequest = await this.requestService.acceptResponse(
        requestId,
        responseId,
        user.id // user.id of the request's creator
      );

      // Notify the host whose response was accepted
      // The accepted response details are in updatedRequest.acceptedResponseId
      // We need to get the host ID from the acceptedResponseId
      if (
        updatedRequest.acceptedResponseId &&
        updatedRequest.acceptedResponseId.host
      ) {
        const hostIdToNotify = updatedRequest.acceptedResponseId.host._id
          ? updatedRequest.acceptedResponseId.host._id.toString()
          : updatedRequest.acceptedResponseId.host.toString();

        // WebSocket notification for accepted response removed
      }

      // WebSocket notification for request update removed

      // Optionally, notify other hosts whose responses were not chosen for this request.
      // This would involve fetching other responses for the requestId and notifying their hosts.

      res.json(updatedRequest);
    } catch (error) {
      next(error);
    }
  };

  getUserRequests = async (req, res, next) => {
    try {
      const { user } = req;
      const { status, page = 1, limit = 20 } = req.query;

      const requests = await this.requestService.getUserRequests(user.id, {
        status,
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
      });

      res.json(requests);
    } catch (error) {
      next(error);
    }
  };

  getResponsesForRequest = async (req, res, next) => {
    try {
      const { requestId } = req.params;
      const { page = 1, limit = 20 } = req.query;

      // Optional: Add verification here to ensure the user making this API call
      // is the one who created the request or has other permissions.
      // For now, assuming public or verified by `verifyToken` if it checks ownership.

      const responses = await this.responseService.getResponsesForRequest(
        requestId,
        {
          page: parseInt(page, 10),
          limit: parseInt(limit, 10),
        }
      );
      res.json(responses);
    } catch (error) {
      next(error);
    }
  };

  getHostResponses = async (req, res, next) => {
    try {
      const { user } = req; // This is the host
      const { status, page = 1, limit = 20 } = req.query;

      const responses = await this.responseService.getHostResponses(user.id, {
        status,
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
      });

      res.json(responses);
    } catch (error) {
      next(error);
    }
  };
}

module.exports = new RequestController();
