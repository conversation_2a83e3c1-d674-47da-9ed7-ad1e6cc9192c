{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nodemon index.js", "start": "NODE_ENV=production node server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.7.9", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-session": "^1.18.0", "express-validator": "^7.1.0", "fs": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "mongoose": "^8.5.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.14", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "socket.io": "^4.8.1", "stripe": "^17.7.0", "uploadthing": "^7.6.0", "uuid": "^10.0.0"}, "devDependencies": {"nodemon": "^3.1.4"}}