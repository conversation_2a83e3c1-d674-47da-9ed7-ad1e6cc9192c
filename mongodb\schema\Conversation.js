const mongoose = require("mongoose");

const ConversationSchema = new mongoose.Schema(
  {
    // User participant (customer)
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "users",
      required: true,
    },
    // Host participant (food provider)
    host: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "users",
      required: true,
    },
    // Reference to the related order
    order: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Order",
      required: false,
    },
    // Last message in the conversation
    lastMessage: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Message",
      default: null,
    },
    // Status of the conversation
    status: {
      type: String,
      enum: ["active", "archived"],
      default: "active",
    },
    // Metadata for conversation
    metadata: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    // Host profile reference for easy access
    hostProfile: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Host",
    },
  },
  { timestamps: true }
);

// Create indexes for efficient querying
ConversationSchema.index({ user: 1, status: 1 });
ConversationSchema.index({ host: 1, status: 1 });
ConversationSchema.index({ order: 1 }, { unique: true });
ConversationSchema.index({ status: 1 });

// Virtual field to get all participants
ConversationSchema.virtual("participants").get(function () {
  return [this.user, this.host];
});

// Enable virtuals in JSON
ConversationSchema.set("toJSON", { virtuals: true });
ConversationSchema.set("toObject", { virtuals: true });

module.exports = ConversationSchema;
