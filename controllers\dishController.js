const {
  createDish,
  getAllDishes,
  getDishById,
  updateDish,
  deleteDish,
  getDishesByCuisine,
} = require("../services/dishService");
const { getHostByDishId } = require("../services/hostService");
const AppError = require("../utils/error");
const {
  BAD_REQUEST,
  INTERNAL_SERVER,
  NOT_FOUND,
} = require("../constants/errorCodes");
const { getCart } = require("./cartController");

/**
 * Controller to create a new dish
 */
const createDishController = async (req, res, next) => {
  try {
    const dishData = req.body;
    const newDish = await createDish(dishData);

    res.status(201).json({
      message: "Dish created successfully",
      data: newDish,
    });
  } catch (error) {
    console.log("Error in createDishController ===>", error);
    return next(
      new AppError({ message: "Failed to create dish", error }, BAD_REQUEST)
    );
  }
};

/**
 * Controller to get all dishes
 */
const getAllDishesController = async (req, res, next) => {
  try {
    // Debug log to check if dineInAvailable is present in the dish response
    console.log(
      "Sample dish response:",
      dishes && dishes[0] ? dishes[0] : "No dishes found"
    );
    // Extract filters from query parameters
    const {
      published,
      premade,
      address,
      dishName,
      title,
      offering,
      cuisine,
      // Location-based filtering parameters
      latitude,
      longitude,
      radius,
      radiusUnit,
      // Map bounds parameters
      north,
      south,
      east,
      west,
      // Currency parameter
      currency,
    } = req.query;

    // Parse query parameters to appropriate types
    const filters = {};

    // Basic filters
    if (published !== undefined) {
      filters.published = published === "true";
    }
    if (premade !== undefined) {
      filters.premade = premade === "true";
    }

    // Search filters
    if (dishName) {
      filters.name = { $regex: dishName, $options: "i" };
    }
    if (offering) {
      filters.offering = offering;
    }
    if (cuisine) {
      // We'll handle cuisine in the service layer since it's a reference
      filters.cuisineQuery = cuisine;
    }

    // Build location filters
    const locationFilters = {};

    // Parse numeric location parameters
    if (
      latitude !== undefined &&
      longitude !== undefined &&
      radius !== undefined
    ) {
      const lat = parseFloat(latitude);
      const lon = parseFloat(longitude);
      const rad = parseFloat(radius);

      if (!isNaN(lat) && !isNaN(lon) && !isNaN(rad)) {
        locationFilters.latitude = lat;
        locationFilters.longitude = lon;
        locationFilters.radius = rad;
        if (radiusUnit && (radiusUnit === "km" || radiusUnit === "miles")) {
          locationFilters.radiusUnit = radiusUnit;
        }
      }
    }

    // Parse map bounds parameters
    if (
      north !== undefined &&
      south !== undefined &&
      east !== undefined &&
      west !== undefined
    ) {
      const n = parseFloat(north);
      const s = parseFloat(south);
      const e = parseFloat(east);
      const w = parseFloat(west);

      if (!isNaN(n) && !isNaN(s) && !isNaN(e) && !isNaN(w)) {
        locationFilters.bounds = {
          north: n,
          south: s,
          east: e,
          west: w,
        };
      }
    }

    // Get dishes with applied filters
    const dishes = await getAllDishes(
      filters,
      currency || "USD",
      locationFilters
    );

    res.status(200).json({
      message: "Dishes retrieved successfully",
      data: dishes,
    });
  } catch (error) {
    console.log("Error in getAllDishesController ===>", error);
    return next(
      new AppError({ message: "Failed to retrieve dishes" }, INTERNAL_SERVER)
    );
  }
};

/**
 * Controller to get a dish by ID
 */
const getDishByIdController = async (req, res, next) => {
  try {
    const { id } = req.params;
    const dish = await getDishById(id);
    const host = await getHostByDishId(id);
    if (!dish) {
      return next(new AppError({ message: "Dish not found" }, NOT_FOUND));
    }

    res.status(200).json({
      message: "Dish retrieved successfully",
      data: dish,
      host,
    });
  } catch (error) {
    console.log("Error in getDishByIdController ===>", error);
    return next(
      new AppError({ message: "Failed to retrieve dish" }, INTERNAL_SERVER)
    );
  }
};

/**
 * Controller to update a dish
 */
const updateDishController = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const updatedDish = await updateDish(id, updateData);

    if (!updatedDish) {
      return next(new AppError({ message: "Dish not found" }, NOT_FOUND));
    }

    res.status(200).json({
      message: "Dish updated successfully",
      data: updatedDish,
    });
  } catch (error) {
    console.log("Error in updateDishController ===>", error);
    return next(
      new AppError({ message: "Failed to update dish" }, BAD_REQUEST)
    );
  }
};

/**
 * Controller to delete a dish
 */
const deleteDishController = async (req, res, next) => {
  try {
    const { id } = req.params;

    const deletedDish = await deleteDish(id);

    if (!deletedDish) {
      return next(new AppError({ message: "Dish not found" }, NOT_FOUND));
    }

    res.status(200).json({
      message: "Dish deleted successfully",
    });
  } catch (error) {
    console.log("Error in deleteDishController ===>", error);
    return next(
      new AppError({ message: "Failed to delete dish" }, INTERNAL_SERVER)
    );
  }
};

/**
 *  Get Dishes by Cuisine
 */
const getDishesByCuisineController = async (req, res, next) => {
  try {
    const { cuisine } = req.params;
    const dishes = await getDishesByCuisine(cuisine);

    res.status(200).json({
      message: "Dishes retrieved successfully",
      data: dishesWithDineInStatus,
    });
  } catch (error) {
    console.log("Error in getDishesByCuisineController ===>", error);
    return next(
      new AppError({ message: "Failed to retrieve dishes" }, INTERNAL_SERVER)
    );
  }
};

module.exports = {
  createDishController,
  getAllDishesController,
  getDishByIdController,
  updateDishController,
  deleteDishController,
  getDishesByCuisineController,
};
